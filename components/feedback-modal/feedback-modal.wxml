<!-- components/feedback-modal/feedback-modal.wxml -->
<view class="feedback-modal" wx:if="{{visible}}">
  <view class="modal-mask" bindtap="onMaskTap"></view>
  <view class="modal-content">
    <!-- 头部 -->
    <view class="modal-header">
      <text class="modal-title">问题反馈</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- 内容 -->
    <view class="modal-body">
      <!-- 问题类型 -->
      <view class="form-group">
        <text class="form-label">问题类型</text>
        <picker 
          mode="selector" 
          range="{{typeOptions}}" 
          range-key="label"
          value="{{selectedType}}" 
          bindchange="onTypeChange"
        >
          <view class="picker-container">
            <text class="picker-text">
              {{typeOptions[selectedType].label}}
            </text>
            <image class="picker-arrow" src="/images/icons/arrow-down.png" mode="aspectFit"></image>
          </view>
        </picker>
      </view>

      <!-- 问题描述 -->
      <view class="form-group">
        <text class="form-label">问题描述 <text class="required">*</text></text>
        <textarea 
          class="form-textarea"
          placeholder="请详细描述您遇到的问题..."
          value="{{description}}"
          bindinput="onDescriptionInput"
          maxlength="500"
          show-confirm-bar="{{false}}"
        ></textarea>
        <view class="char-count">{{description.length}}/500</view>
      </view>

      <!-- 联系方式 -->
      <view class="form-group">
        <text class="form-label">联系方式</text>
        <input 
          class="form-input"
          type="text"
          placeholder="请输入手机号或微信号（可选）"
          value="{{contact}}"
          bindinput="onContactInput"
        />
      </view>

      <!-- 错误信息（仅错误反馈时显示） -->
      <view class="form-group" wx:if="{{errorInfo}}">
        <text class="form-label">错误信息</text>
        <view class="error-info">
          <view class="error-item">
            <text class="error-label">错误类型:</text>
            <text class="error-value">{{errorInfo.type}}</text>
          </view>
          <view class="error-item">
            <text class="error-label">错误代码:</text>
            <text class="error-value">{{errorInfo.code}}</text>
          </view>
          <view class="error-item">
            <text class="error-label">错误消息:</text>
            <text class="error-value">{{errorInfo.message}}</text>
          </view>
          <view class="error-item">
            <text class="error-label">发生时间:</text>
            <text class="error-value">{{errorInfo.timestamp}}</text>
          </view>
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="form-group">
        <text class="form-label">相关截图（可选）</text>
        <view class="image-upload">
          <view class="upload-list">
            <view 
              class="upload-item" 
              wx:for="{{images}}" 
              wx:key="index"
            >
              <image 
                class="upload-image" 
                src="{{item}}" 
                mode="aspectFill"
                bindtap="onPreviewImage"
                data-url="{{item}}"
                data-urls="{{images}}"
              ></image>
              <view 
                class="delete-btn" 
                bindtap="onDeleteImage" 
                data-index="{{index}}"
              >
                <text class="delete-icon">×</text>
              </view>
            </view>
            
            <!-- 添加图片按钮 -->
            <view 
              class="upload-btn" 
              wx:if="{{images.length < 3}}"
              bindtap="onChooseImage"
            >
              <image class="upload-icon" src="/images/icons/camera.png" mode="aspectFit"></image>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
          <view class="upload-tip">最多上传3张图片，每张不超过5MB</view>
        </view>
      </view>
    </view>

    <!-- 底部 -->
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onCancel">取消</button>
      <button 
        class="submit-btn {{!canSubmit ? 'disabled' : ''}}" 
        bindtap="onSubmit"
        disabled="{{!canSubmit}}"
        loading="{{submitting}}"
      >
        {{submitting ? '提交中...' : '提交反馈'}}
      </button>
    </view>
  </view>
</view>