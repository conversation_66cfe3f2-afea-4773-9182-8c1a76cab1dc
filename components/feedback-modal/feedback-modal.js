/**
 * 问题反馈模态框组件
 * Feedback Modal Component
 */

Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    errorInfo: {
      type: Object,
      value: null
    }
  },

  data: {
    selectedType: 0,
    typeOptions: [
      { value: 'bug', label: '功能异常' },
      { value: 'feature', label: '功能建议' },
      { value: 'ui', label: '界面问题' },
      { value: 'performance', label: '性能问题' },
      { value: 'other', label: '其他问题' }
    ],
    description: '',
    contact: '',
    images: [],
    submitting: false
  },

  computed: {
    canSubmit() {
      return this.data.description.trim().length >= 10;
    }
  },

  methods: {
    /**
     * 关闭模态框
     */
    onClose() {
      this.setData({
        visible: false,
        description: '',
        contact: '',
        images: [],
        selectedType: 0,
        submitting: false
      });
      
      this.triggerEvent('close');
    },

    /**
     * 点击遮罩关闭
     */
    onMaskTap() {
      this.onClose();
    },

    /**
     * 类型选择改变
     */
    onTypeChange(e) {
      this.setData({
        selectedType: parseInt(e.detail.value)
      });
    },

    /**
     * 描述输入
     */
    onDescriptionInput(e) {
      this.setData({
        description: e.detail.value
      });
    },

    /**
     * 联系方式输入
     */
    onContactInput(e) {
      this.setData({
        contact: e.detail.value
      });
    },

    /**
     * 选择图片
     */
    onChooseImage() {
      const remainingCount = 3 - this.data.images.length;
      
      wx.chooseMedia({
        count: remainingCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        maxDuration: 30,
        camera: 'back',
        success: (res) => {
          const tempFiles = res.tempFiles || [];
          const newImages = tempFiles.map(file => file.tempFilePath);
          
          // 检查文件大小
          const validImages = [];
          for (const file of tempFiles) {
            if (file.size <= 5 * 1024 * 1024) { // 5MB限制
              validImages.push(file.tempFilePath);
            } else {
              wx.showToast({
                title: '图片不能超过5MB',
                icon: 'error'
              });
            }
          }

          this.setData({
            images: [...this.data.images, ...validImages]
          });
        },
        fail: (err) => {
          if (err.errMsg.includes('cancel')) return;
          
          wx.showToast({
            title: '选择图片失败',
            icon: 'error'
          });
        }
      });
    },

    /**
     * 预览图片
     */
    onPreviewImage(e) {
      const { url, urls } = e.currentTarget.dataset;
      
      wx.previewImage({
        current: url,
        urls: urls
      });
    },

    /**
     * 删除图片
     */
    onDeleteImage(e) {
      const { index } = e.currentTarget.dataset;
      const images = [...this.data.images];
      images.splice(index, 1);
      
      this.setData({
        images
      });
    },

    /**
     * 取消
     */
    onCancel() {
      this.onClose();
    },

    /**
     * 提交反馈
     */
    async onSubmit() {
      if (!this.data.canSubmit || this.data.submitting) return;

      const { selectedType, typeOptions, description, contact, images, errorInfo } = this.data;

      // 验证必填项
      if (description.trim().length < 10) {
        wx.showToast({
          title: '问题描述至少10个字符',
          icon: 'error'
        });
        return;
      }

      this.setData({ submitting: true });

      try {
        // 上传图片
        const imageUrls = await this.uploadImages(images);

        // 准备提交数据
        const feedbackData = {
          type: typeOptions[selectedType].value,
          typeLabel: typeOptions[selectedType].label,
          description: description.trim(),
          contact: contact.trim(),
          images: imageUrls,
          errorInfo,
          timestamp: new Date().toISOString(),
          userInfo: getApp().globalData.userInfo || {},
          systemInfo: wx.getSystemInfoSync(),
          version: getApp().version || '1.0.0'
        };

        // 提交反馈
        await this.submitFeedback(feedbackData);

        wx.showToast({
          title: '反馈提交成功',
          icon: 'success'
        });

        this.onClose();
        this.triggerEvent('success', feedbackData);

      } catch (error) {
        console.error('提交反馈失败:', error);
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'error'
        });
      } finally {
        this.setData({ submitting: false });
      }
    },

    /**
     * 上传图片
     * @param {Array} images 图片路径数组
     */
    async uploadImages(images) {
      if (!images || images.length === 0) return [];

      const uploadPromises = images.map(imagePath => {
        return new Promise((resolve, reject) => {
          wx.uploadFile({
            url: `${getApp().globalData.baseUrl}/api/feedback/upload`,
            filePath: imagePath,
            name: 'image',
            header: {
              'Authorization': `Bearer ${getApp().globalData.token}`
            },
            success: (res) => {
              try {
                const result = JSON.parse(res.data);
                if (result.success) {
                  resolve(result.data.url);
                } else {
                  reject(new Error(result.message));
                }
              } catch (e) {
                reject(new Error('上传响应解析失败'));
              }
            },
            fail: reject
          });
        });
      });

      return Promise.all(uploadPromises);
    },

    /**
     * 提交反馈数据
     * @param {Object} feedbackData 反馈数据
     */
    async submitFeedback(feedbackData) {
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${getApp().globalData.baseUrl}/api/feedback`,
          method: 'POST',
          data: feedbackData,
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getApp().globalData.token}`
          },
          success: (res) => {
            if (res.statusCode === 200 && res.data.success) {
              resolve(res.data);
            } else {
              reject(new Error(res.data.message || '提交失败'));
            }
          },
          fail: reject
        });
      });
    }
  },

  observers: {
    'description': function(value) {
      this.setData({
        canSubmit: value.trim().length >= 10
      });
    }
  }
});