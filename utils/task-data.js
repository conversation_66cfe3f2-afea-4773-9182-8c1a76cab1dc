// utils/task-data.js
// 统一的待办任务数据管理

/**
 * 待办任务数据源
 * 统一管理所有待办任务数据，确保首页、任务页面等各处数据一致
 */

// 基础任务数据（默认为空，防疫任务将根据入栏记录动态生成）
const taskData = [];

// 防疫流程模板数据 - 根据Excel文件精确拆解到每一天
const vaccinationTemplate = [
  // 第1天
  {
    dayAge: 1,
    title: '开口用药（第1天）',
    description: '3%葡萄糖或3%红糖水+电解多维饮水，增强抵抗力，补充水分，减少应激',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第2天
  {
    dayAge: 2,
    title: '小鹅瘟抗体注射 + 开口药（第2天）',
    description: '小鹅瘟高免血清或高免蛋黄抗体（第一针：每只1.2毫升）+ 开口药连喂第2天',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第3天
  {
    dayAge: 3,
    title: '开口药（第3天）',
    description: '开口药连喂第3天，预防雏鹅沙门氏菌、大肠杆菌等，促进卵黄吸收，抗应激',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第4天
  {
    dayAge: 4,
    title: '开口药（第4天）',
    description: '开口药连喂第4天，预防雏鹅沙门氏菌、大肠杆菌等，促进卵黄吸收，抗应激',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第5天
  {
    dayAge: 5,
    title: '开口药最后一天 + 开始控料',
    description: '开口药连喂最后一天。从今天开始控料直到15日龄：加青饲料，不用豆饼等蛋白饲料，晚上12点后停饲料或全喂青草/玉米面',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第6天
  {
    dayAge: 6,
    title: '小鹅瘟防疫（第二针）+ 控料第2天',
    description: '小鹅瘟防疫（第二针：痛风+呼肠孤+小鹅瘟疫苗+浆膜炎，每只1毫升）+ 白天饮多维防应激。做完疫苗后观察棚内湿度保持60%以下',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第7天
  {
    dayAge: 7,
    title: '保肝护肾（第1天）+ 控料第3天',
    description: '痛清2瓶/天，上午集中2小时饮用（控水半小时后集中2-3小时饮完）+ 晚上10点后千分之三小苏打通肝肾 + 控料继续',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第8天
  {
    dayAge: 8,
    title: '保肝护肾（第2天）+ 控料第4天',
    description: '痛清2瓶/天，上午集中2小时饮用（控水半小时后集中2-3小时饮完）+ 晚上10点后千分之三小苏打通肝肾 + 控料继续',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第9天
  {
    dayAge: 9,
    title: '预防呼肠孤病毒（第1天）+ 控料第5天',
    description: '呼肠清1瓶，上午集中2小时饮用（控水半小时后集中2-3小时饮完）+ 控料继续',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第10天
  {
    dayAge: 10,
    title: '预防呼肠孤病毒（第2天）+ 控料第6天',
    description: '呼肠清1瓶，上午集中2小时饮用（控水半小时后集中2-3小时饮完）+ 控料继续',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第11天
  {
    dayAge: 11,
    title: '控料第7天',
    description: '继续控料：加青饲料，不用豆饼等蛋白饲料，晚上12点后停饲料或全喂青草/玉米面',
    priority: 'medium',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第12天
  {
    dayAge: 12,
    title: '预防浆膜炎（第1天）+ 控料第8天',
    description: '亿消2号+鸭乐2号各半袋，上午集中2-3小时饮用（控水半小时后集中2-3小时饮完）+ 晚上12点后千分之三小苏打通肝肾 + 控料继续',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第13天
  {
    dayAge: 13,
    title: '预防浆膜炎（第2天）+ 控料第9天',
    description: '亿消2号+鸭乐2号各半袋，上午集中2-3小时饮用（控水半小时后集中2-3小时饮完）+ 晚上12点后千分之三小苏打通肝肾 + 控料继续',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第14天
  {
    dayAge: 14,
    title: '控料第10天',
    description: '继续控料：加青饲料，不用豆饼等蛋白饲料，晚上12点后停饲料或全喂青草/玉米面',
    priority: 'medium',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第15天
  {
    dayAge: 15,
    title: '控料最后一天',
    description: '控料最后一天：加青饲料，不用豆饼等蛋白饲料，晚上12点后停饲料或全喂青草/玉米面。明天开始恢复正常饲喂',
    priority: 'medium',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第16天
  {
    dayAge: 16,
    title: '预防病毒性感冒（第1天）+ 恢复正常饲喂',
    description: '增强素1包+浆速1包，上午集中3小时饮用（控水半小时后集中2-3小时饮完）。16日龄开始无需控料，恢复正常饲喂',
    priority: 'medium',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第17天
  {
    dayAge: 17,
    title: '预防病毒性感冒（第2天）',
    description: '增强素1包+浆速1包，上午集中3小时饮用（控水半小时后集中2-3小时饮完）',
    priority: 'medium',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第18天
  {
    dayAge: 18,
    title: '预防痛风',
    description: '观察是否拉稀（黄白便、水便、黑便），如有异常可投喂畅清，正常则无需用药。晚上使用千分之三小苏打通肝肾',
    priority: 'medium',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第19天
  {
    dayAge: 19,
    title: '日常观察',
    description: '观察鹅群健康状况，注意精神状态、采食量、粪便情况',
    priority: 'low',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第20天
  {
    dayAge: 20,
    title: '副粘+禽流感疫苗（第三针）',
    description: '副粘+禽流感H9+安卡拉（三联苗）每只1毫升，做完疫苗水中加多维缓解应激',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第21天
  {
    dayAge: 21,
    title: '预防感冒',
    description: '观察是否有甩头流涕咳嗽情况，如有可喂亿消2号，无症状则不用药。晚上使用千分之三小苏打通肝肾',
    priority: 'medium',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第22天
  {
    dayAge: 22,
    title: '预防呼吸道疾病（第1天）',
    description: '呼畅1瓶，下午集中3小时饮用（控水半小时后集中2-3小时饮完），预防小鹅咳嗽呼噜感冒',
    priority: 'medium',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第23天
  {
    dayAge: 23,
    title: '预防呼吸道疾病（第2天）',
    description: '呼畅1瓶，下午集中3小时饮用（控水半小时后集中2-3小时饮完），预防小鹅咳嗽呼噜感冒',
    priority: 'medium',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第24天
  {
    dayAge: 24,
    title: '日常观察',
    description: '观察鹅群健康状况，注意精神状态、采食量、粪便情况',
    priority: 'low',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第25天
  {
    dayAge: 25,
    title: '日常观察',
    description: '观察鹅群健康状况，注意精神状态、采食量、粪便情况',
    priority: 'low',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第26天
  {
    dayAge: 26,
    title: '预防肠炎+抗病毒（第1天）',
    description: '肠速清1包+增强素1袋，上午集中3小时饮用（控水半小时后集中2-3小时饮完）。重点预防肠炎，大杆高峰期',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第27天
  {
    dayAge: 27,
    title: '预防肠炎+抗病毒（第2天）',
    description: '肠速清1包+增强素1袋，上午集中3小时饮用（控水半小时后集中2-3小时饮完）。重点预防肠炎，大杆高峰期',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第28天
  {
    dayAge: 28,
    title: '日常观察',
    description: '观察鹅群健康状况，注意精神状态、采食量、粪便情况',
    priority: 'low',
    assignee: '饲养员',
    category: '防疫流程'
  },
  // 第29天
  {
    dayAge: 29,
    title: '抗病毒治疗（第1天）',
    description: '亿消2号1袋，上午集中3小时饮用（控水半小时后集中2-3小时饮完），抗病毒、流感、感冒、副黏病毒、黄病毒',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  },
  // 第30天
  {
    dayAge: 30,
    title: '抗病毒治疗（第2天）+ 第一个月防疫完成',
    description: '亿消2号1袋，上午集中3小时饮用（控水半小时后集中2-3小时饮完）。完成第一个月关键防疫，后续可平稳养殖，30天后抗生素可备氟苯尼考、多西环素、替米考星、硫酸新霉素',
    priority: 'high',
    assignee: '防疫员',
    category: '防疫流程'
  }
];

/**
 * 计算日龄
 * @param {string} entryDate 入栏日期 (YYYY-MM-DD)
 * @returns {number} 当前日龄
 */
function calculateDayAge(entryDate) {
  if (!entryDate) return 0;
  
  const entry = new Date(entryDate);
  const today = new Date();
  const diffTime = today - entry;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
}

/**
 * 根据入栏记录生成防疫任务（仅当天任务）
 * @param {string} entryDate 入栏日期
 * @param {string} flockId 鹅群ID
 * @param {string} flockName 鹅群名称
 * @returns {Array} 当前需要执行的防疫任务
 */
function generateVaccinationTasks(entryDate, flockId, flockName) {
  if (!entryDate) return [];
  
  const currentDayAge = calculateDayAge(entryDate);
  const today = new Date();
  
  // 只获取当天需要执行的防疫任务
  const todayTasks = vaccinationTemplate.filter(template => {
    return template.dayAge === currentDayAge;
  });
  
  // 转换为待办任务格式
  return todayTasks.map((template, index) => {
    const taskDate = new Date(entryDate);
    taskDate.setDate(taskDate.getDate() + template.dayAge - 1);
    
    // 只显示当天的任务，不显示日龄信息
    let status = 'pending';
    let timeText = '今天';
    
    return {
      id: `vaccination_${flockId}_${template.dayAge}`,
      title: template.title,
      description: `【${flockName}】${template.description}`,
      time: timeText,
      type: 'vaccination',
      status: status,
      statusText: '待处理',
      priority: template.priority,
      priorityText: template.priority === 'high' ? '高' : template.priority === 'medium' ? '中' : '低',
      deadline: taskDate.toISOString().split('T')[0],
      assignee: template.assignee,
      category: template.category,
      dayAge: template.dayAge,
      flockId: flockId,
      flockName: flockName,
      entryDate: entryDate,
      currentDayAge: currentDayAge,
      createTime: new Date().toISOString(),
      completed: false
    };
  });
}

/**
 * 根据入栏记录生成所有防疫任务（完整流程）
 * @param {string} entryDate 入栏日期
 * @param {string} flockId 鹅群ID
 * @param {string} flockName 鹅群名称
 * @returns {Array} 完整的防疫任务流程
 */
function generateAllVaccinationTasks(entryDate, flockId, flockName) {
  if (!entryDate) return [];
  
  const currentDayAge = calculateDayAge(entryDate);
  const today = new Date();
  
  // 生成所有防疫任务，不仅仅是当天的
  return vaccinationTemplate.map((template, index) => {
    const taskDate = new Date(entryDate);
    taskDate.setDate(taskDate.getDate() + template.dayAge - 1);
    
    // 根据日龄确定任务状态和时间显示
    let status = 'pending';
    let timeText = '';
    
    if (template.dayAge < currentDayAge) {
      status = 'overdue';
      timeText = `第${template.dayAge}天（已过期）`;
    } else if (template.dayAge === currentDayAge) {
      status = 'pending';
      timeText = `第${template.dayAge}天（今天）`;
    } else {
      status = 'pending';
      timeText = `第${template.dayAge}天`;
    }
    
    return {
      id: `vaccination_${flockId}_${template.dayAge}`,
      title: template.title,
      description: `【${flockName}】${template.description}`,
      time: timeText,
      type: 'vaccination',
      status: status,
      statusText: status === 'overdue' ? '已逾期' : '待处理',
      priority: template.priority,
      priorityText: template.priority === 'high' ? '高' : template.priority === 'medium' ? '中' : '低',
      deadline: taskDate.toISOString().split('T')[0],
      assignee: template.assignee,
      category: template.category,
      dayAge: template.dayAge,
      flockId: flockId,
      flockName: flockName,
      entryDate: entryDate,
      currentDayAge: currentDayAge,
      createTime: new Date().toISOString(),
      completed: false
    };
  });
}

/**
 * 获取入栏记录
 * @returns {Array} 入栏记录列表
 */
function getFlockEntryRecords() {
  try {
    const records = wx.getStorageSync('flock_entry_records') || [];
    return Array.isArray(records) ? records : [];
  } catch (error) {
    console.error('获取入栏记录失败:', error);
    return [];
  }
}

/**
 * 获取首页展示的待办任务（前3条）
 * @param {number} limit 限制数量，默认3条
 * @returns {Array} 待办任务列表
 */
function getHomeTaskList(limit = 3) {
  try {
    // 获取入栏记录
    const flockRecords = getFlockEntryRecords();
    
    if (flockRecords.length === 0) {
      // 没有入栏记录时不显示任何任务
      return [];
    }
    
    // 为每个入栏记录生成防疫任务
    let allVaccinationTasks = [];
    flockRecords.forEach(record => {
      const tasks = generateVaccinationTasks(record.entryDate, record.id, record.flockName);
      allVaccinationTasks = allVaccinationTasks.concat(tasks);
    });
    
    // 合并基础任务和防疫任务
    const allTasks = [...taskData, ...allVaccinationTasks];
    
    // 从本地存储读取任务完成状态
    const completedTasks = wx.getStorageSync('completed_tasks') || {};
    
    // 过滤未完成的任务并按优先级和日龄排序
    const pendingTasks = allTasks
      .filter(task => {
        const taskStatus = completedTasks[task.id];
        return !taskStatus || !taskStatus.completed;
      })
      .sort((a, b) => {
        // 首先按优先级排序
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        
        // 优先级相同时，按日龄排序
        if (a.dayAge && b.dayAge) {
          return a.dayAge - b.dayAge;
        }
        
        return 0;
      })
      .slice(0, limit);

    // 更新任务状态
    return pendingTasks.map(task => {
      const taskStatus = completedTasks[task.id];
      if (taskStatus && taskStatus.completed) {
        return {
          ...task,
          completed: true,
          completedTime: taskStatus.completedTime
        };
      }
      return task;
    });
  } catch (error) {
    console.error('获取首页任务列表失败:', error);
    return [];
  }
}

/**
 * 获取所有待办任务
 * @returns {Array} 所有待办任务列表
 */
function getAllTasks() {
  try {
    // 获取入栏记录
    const flockRecords = getFlockEntryRecords();
    
    if (flockRecords.length === 0) {
      // 没有入栏记录时只返回基础任务
      return taskData;
    }
    
    // 为每个入栏记录生成完整的防疫任务
    let allVaccinationTasks = [];
    flockRecords.forEach(record => {
      const tasks = generateAllVaccinationTasks(record.entryDate, record.id, record.flockName);
      allVaccinationTasks = allVaccinationTasks.concat(tasks);
    });
    
    // 合并基础任务和防疫任务
    const allTasks = [...taskData, ...allVaccinationTasks];
    
    // 从本地存储读取任务完成状态
    const completedTasks = wx.getStorageSync('completed_tasks') || {};
    
    // 更新任务状态
    return allTasks.map(task => {
      const taskStatus = completedTasks[task.id];
      if (taskStatus && taskStatus.completed) {
        return {
          ...task,
          completed: true,
          completedTime: taskStatus.completedTime,
          status: 'completed',
          statusText: '已完成'
        };
      }
      return task;
    });
  } catch (error) {
    console.error('获取所有任务失败:', error);
    return taskData;
  }
}

/**
 * 根据状态筛选任务
 * @param {string} status 任务状态
 * @returns {Array} 筛选后的任务列表
 */
function getTasksByStatus(status) {
  const allTasks = getAllTasks();
  if (status === 'all') {
    return allTasks;
  }
  return allTasks.filter(task => task.status === status);
}

/**
 * 根据类型筛选任务
 * @param {string} type 任务类型
 * @returns {Array} 筛选后的任务列表
 */
function getTasksByType(type) {
  const allTasks = getAllTasks();
  if (type === 'all') {
    return allTasks;
  }
  return allTasks.filter(task => task.type === type);
}

/**
 * 搜索任务
 * @param {string} keyword 搜索关键词
 * @returns {Array} 搜索结果
 */
function searchTasks(keyword) {
  const allTasks = getAllTasks();
  if (!keyword) {
    return allTasks;
  }
  
  const lowerKeyword = keyword.toLowerCase();
  return allTasks.filter(task => 
    task.title.toLowerCase().includes(lowerKeyword) ||
    task.description.toLowerCase().includes(lowerKeyword) ||
    task.category.toLowerCase().includes(lowerKeyword)
  );
}

/**
 * 完成任务
 * @param {number} taskId 任务ID
 * @param {string} completedTime 完成时间
 */
function completeTask(taskId, completedTime = new Date().toISOString()) {
  const completedTasks = wx.getStorageSync('completed_tasks') || {};
  completedTasks[taskId] = {
    completed: true,
    completedTime: completedTime
  };
  wx.setStorageSync('completed_tasks', completedTasks);
}

/**
 * 取消完成任务
 * @param {number} taskId 任务ID
 */
function uncompleteTask(taskId) {
  const completedTasks = wx.getStorageSync('completed_tasks') || {};
  delete completedTasks[taskId];
  wx.setStorageSync('completed_tasks', completedTasks);
}

/**
 * 获取任务统计信息
 * @returns {Object} 任务统计
 */
function getTaskStats() {
  const allTasks = getAllTasks();
  const stats = {
    total: allTasks.length,
    pending: allTasks.filter(task => task.status === 'pending').length,
    processing: allTasks.filter(task => task.status === 'processing').length,
    completed: allTasks.filter(task => task.completed).length,
    overdue: allTasks.filter(task => task.status === 'overdue').length
  };
  
  return stats;
}

/**
 * 添加入栏记录
 * @param {Object} record 入栏记录 {id, flockName, entryDate, quantity}
 */
function addFlockEntryRecord(record) {
  try {
    const records = getFlockEntryRecords();
    const newRecord = {
      id: record.id || Date.now().toString(),
      flockName: record.flockName,
      entryDate: record.entryDate,
      quantity: record.quantity || 0,
      createTime: new Date().toISOString()
    };
    
    records.push(newRecord);
    wx.setStorageSync('flock_entry_records', records);
    
    return newRecord;
  } catch (error) {
    console.error('添加入栏记录失败:', error);
    return null;
  }
}

/**
 * 删除入栏记录
 * @param {string} recordId 记录ID
 */
function removeFlockEntryRecord(recordId) {
  try {
    const records = getFlockEntryRecords();
    const filteredRecords = records.filter(record => record.id !== recordId);
    wx.setStorageSync('flock_entry_records', filteredRecords);
    
    // 同时删除相关的已完成任务记录
    const completedTasks = wx.getStorageSync('completed_tasks') || {};
    Object.keys(completedTasks).forEach(taskId => {
      if (taskId.startsWith(`vaccination_${recordId}_`)) {
        delete completedTasks[taskId];
      }
    });
    wx.setStorageSync('completed_tasks', completedTasks);
    
    return true;
  } catch (error) {
    console.error('删除入栏记录失败:', error);
    return false;
  }
}

module.exports = {
  getHomeTaskList,
  getAllTasks,
  getTasksByStatus,
  getTasksByType,
  searchTasks,
  completeTask,
  uncompleteTask,
  getTaskStats,
  getFlockEntryRecords,
  addFlockEntryRecord,
  removeFlockEntryRecord,
  calculateDayAge,
  generateVaccinationTasks,
  generateAllVaccinationTasks,
  vaccinationTemplate
};