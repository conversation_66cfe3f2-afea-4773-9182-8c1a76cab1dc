/**
 * 智慧养鹅小程序 - 高级缓存管理工具
 * 提供多层缓存、过期管理、内存优化等功能
 */

class AdvancedCache {
  constructor() {
    this.memoryCache = new Map(); // 内存缓存
    this.config = {
      maxMemorySize: 50, // 最大内存缓存条目数
      defaultExpire: 5 * 60 * 1000, // 默认过期时间 5分钟
      storagePrefix: 'cache_', // 本地存储前缀
      cleanupInterval: 10 * 60 * 1000, // 清理间隔 10分钟
      compressionThreshold: 1024 // 压缩阈值 1KB
    };
    
    this.startCleanupTimer();
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {Object} options 选项
   */
  async set(key, data, options = {}) {
    const {
      expire = this.config.defaultExpire,
      storage = true, // 是否持久化到本地存储
      memory = true, // 是否保存到内存缓存
      compress = null, // 是否压缩（自动判断）
      tags = [], // 缓存标签，用于批量清理
      priority = 1 // 优先级，影响清理顺序
    } = options;

    const now = Date.now();
    const expiresAt = expire > 0 ? now + expire : null;
    
    const cacheItem = {
      data,
      createdAt: now,
      expiresAt,
      accessCount: 0,
      lastAccessed: now,
      tags: Array.isArray(tags) ? tags : [],
      priority,
      size: this.calculateSize(data)
    };

    // 内存缓存
    if (memory) {
      this.setMemoryCache(key, cacheItem);
    }

    // 本地存储缓存
    if (storage) {
      await this.setStorageCache(key, cacheItem, compress);
    }

    return true;
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @param {Object} options 选项
   */
  async get(key, options = {}) {
    const {
      defaultValue = null,
      refreshExpire = false, // 是否刷新过期时间
      memoryFirst = true // 优先从内存获取
    } = options;

    let cacheItem = null;

    // 先从内存缓存获取
    if (memoryFirst && this.memoryCache.has(key)) {
      cacheItem = this.memoryCache.get(key);
      
      // 检查过期
      if (this.isExpired(cacheItem)) {
        this.memoryCache.delete(key);
        cacheItem = null;
      }
    }

    // 从本地存储获取
    if (!cacheItem) {
      cacheItem = await this.getStorageCache(key);
      
      if (cacheItem && this.isExpired(cacheItem)) {
        await this.removeStorageCache(key);
        cacheItem = null;
      }
    }

    if (!cacheItem) {
      return defaultValue;
    }

    // 更新访问统计
    cacheItem.accessCount++;
    cacheItem.lastAccessed = Date.now();

    // 刷新过期时间
    if (refreshExpire && cacheItem.expiresAt) {
      const originalExpire = cacheItem.expiresAt - cacheItem.createdAt;
      cacheItem.expiresAt = Date.now() + originalExpire;
    }

    // 更新内存缓存
    if (memoryFirst && !this.memoryCache.has(key)) {
      this.setMemoryCache(key, cacheItem);
    }

    return cacheItem.data;
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  async remove(key) {
    this.memoryCache.delete(key);
    await this.removeStorageCache(key);
    return true;
  }

  /**
   * 批量删除缓存（按标签）
   * @param {string|Array} tags 标签
   */
  async removeByTags(tags) {
    const targetTags = Array.isArray(tags) ? tags : [tags];
    const keysToRemove = [];

    // 检查内存缓存
    for (const [key, item] of this.memoryCache) {
      if (item.tags.some(tag => targetTags.includes(tag))) {
        keysToRemove.push(key);
      }
    }

    // 检查本地存储缓存
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => 
        key.startsWith(this.config.storagePrefix)
      );

      for (const storageKey of cacheKeys) {
        const key = storageKey.replace(this.config.storagePrefix, '');
        if (!keysToRemove.includes(key)) {
          const item = await this.getStorageCache(key);
          if (item && item.tags.some(tag => targetTags.includes(tag))) {
            keysToRemove.push(key);
          }
        }
      }
    } catch (error) {
      console.warn('检查本地存储缓存失败:', error);
    }

    // 删除缓存
    for (const key of keysToRemove) {
      await this.remove(key);
    }

    return keysToRemove.length;
  }

  /**
   * 清理过期缓存
   */
  async cleanup() {
    const now = Date.now();
    let cleanedCount = 0;

    // 清理内存缓存
    for (const [key, item] of this.memoryCache) {
      if (this.isExpired(item)) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    // 清理本地存储缓存
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => 
        key.startsWith(this.config.storagePrefix)
      );

      for (const storageKey of cacheKeys) {
        const key = storageKey.replace(this.config.storagePrefix, '');
        const item = await this.getStorageCache(key);
        
        if (item && this.isExpired(item)) {
          await this.removeStorageCache(key);
          cleanedCount++;
        }
      }
    } catch (error) {
      console.warn('清理本地存储缓存失败:', error);
    }

    return cleanedCount;
  }

  /**
   * 获取缓存统计信息
   */
  async getStats() {
    const memoryStats = {
      count: this.memoryCache.size,
      size: 0
    };

    for (const [key, item] of this.memoryCache) {
      memoryStats.size += item.size || 0;
    }

    let storageStats = {
      count: 0,
      size: 0
    };

    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => 
        key.startsWith(this.config.storagePrefix)
      );

      storageStats.count = cacheKeys.length;

      // 估算存储大小
      for (const key of cacheKeys) {
        try {
          const data = wx.getStorageSync(key);
          storageStats.size += JSON.stringify(data).length;
        } catch (e) {
          // 忽略单个条目的错误
        }
      }
    } catch (error) {
      console.warn('获取存储统计失败:', error);
    }

    return {
      memory: memoryStats,
      storage: storageStats,
      total: {
        count: memoryStats.count + storageStats.count,
        size: memoryStats.size + storageStats.size
      }
    };
  }

  /**
   * 内存缓存管理
   */
  setMemoryCache(key, item) {
    // 检查内存限制
    if (this.memoryCache.size >= this.config.maxMemorySize) {
      this.evictMemoryCache();
    }

    this.memoryCache.set(key, item);
  }

  /**
   * 内存缓存淘汰（LFU + LRU混合策略）
   */
  evictMemoryCache() {
    const entries = Array.from(this.memoryCache.entries());
    
    // 按优先级、访问频率、最近访问时间排序
    entries.sort((a, b) => {
      const [keyA, itemA] = a;
      const [keyB, itemB] = b;
      
      // 优先级（越小越先被淘汰）
      if (itemA.priority !== itemB.priority) {
        return itemA.priority - itemB.priority;
      }
      
      // 访问频率（越少越先被淘汰）
      if (itemA.accessCount !== itemB.accessCount) {
        return itemA.accessCount - itemB.accessCount;
      }
      
      // 最近访问时间（越早越先被淘汰）
      return itemA.lastAccessed - itemB.lastAccessed;
    });

    // 删除25%的条目
    const evictCount = Math.ceil(this.memoryCache.size * 0.25);
    for (let i = 0; i < evictCount && i < entries.length; i++) {
      this.memoryCache.delete(entries[i][0]);
    }
  }

  /**
   * 本地存储缓存操作
   */
  async setStorageCache(key, item, compress = null) {
    try {
      let data = item;
      
      // 决定是否压缩
      if (compress === null) {
        const dataSize = JSON.stringify(item).length;
        compress = dataSize > this.config.compressionThreshold;
      }

      if (compress) {
        data = { ...item, compressed: true };
        // 这里可以添加实际的压缩逻辑
        // data.data = await compressData(item.data);
      }

      wx.setStorageSync(this.config.storagePrefix + key, data);
    } catch (error) {
      console.warn(`设置本地存储缓存失败 ${key}:`, error);
    }
  }

  async getStorageCache(key) {
    try {
      const data = wx.getStorageSync(this.config.storagePrefix + key);
      if (!data) return null;

      // 解压缩
      if (data.compressed) {
        // 这里可以添加实际的解压缩逻辑
        // data.data = await decompressData(data.data);
        delete data.compressed;
      }

      return data;
    } catch (error) {
      console.warn(`获取本地存储缓存失败 ${key}:`, error);
      return null;
    }
  }

  async removeStorageCache(key) {
    try {
      wx.removeStorageSync(this.config.storagePrefix + key);
    } catch (error) {
      console.warn(`删除本地存储缓存失败 ${key}:`, error);
    }
  }

  /**
   * 工具方法
   */
  isExpired(item) {
    if (!item.expiresAt) return false;
    return Date.now() > item.expiresAt;
  }

  calculateSize(data) {
    try {
      return JSON.stringify(data).length;
    } catch (error) {
      return 0;
    }
  }

  startCleanupTimer() {
    setInterval(() => {
      this.cleanup().then(count => {
        if (count > 0) {
          console.log(`清理了 ${count} 个过期缓存`);
        }
      });
    }, this.config.cleanupInterval);
  }

  /**
   * 设置配置
   */
  setConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 清空所有缓存
   */
  async clear() {
    this.memoryCache.clear();
    
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => 
        key.startsWith(this.config.storagePrefix)
      );
      
      for (const key of cacheKeys) {
        wx.removeStorageSync(key);
      }
    } catch (error) {
      console.warn('清空本地存储缓存失败:', error);
    }
  }
}

// 创建全局实例
const cache = new AdvancedCache();

module.exports = cache;