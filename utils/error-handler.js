/**
 * 智慧养鹅小程序 - 统一错误处理工具
 * 提供统一的错误处理、用户反馈和错误上报功能
 */

class ErrorHandler {
  static config = {
    enableConsoleLog: process.env.NODE_ENV === 'development',
    enableErrorReport: true,
    showUserToast: true,
    errorReportUrl: '/api/error-report'
  };

  /**
   * 处理API请求错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文信息
   */
  static handleApiError(error, context = {}) {
    const errorInfo = {
      type: 'API_ERROR',
      message: error.message || '网络请求失败',
      code: error.code || error.statusCode,
      url: context.url,
      method: context.method,
      timestamp: new Date().toISOString(),
      userAgent: getApp().globalData.systemInfo
    };

    this.logError(errorInfo);
    this.reportError(errorInfo);
    this.showUserFeedback(errorInfo);

    return errorInfo;
  }

  /**
   * 处理业务逻辑错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文信息
   */
  static handleBusinessError(error, context = {}) {
    const errorInfo = {
      type: 'BUSINESS_ERROR',
      message: error.message || '操作失败',
      code: error.code,
      module: context.module,
      action: context.action,
      timestamp: new Date().toISOString()
    };

    this.logError(errorInfo);
    
    // 业务错误通常需要显示给用户
    if (this.config.showUserToast) {
      wx.showToast({
        title: errorInfo.message,
        icon: 'error',
        duration: 3000
      });
    }

    return errorInfo;
  }

  /**
   * 处理小程序API错误
   * @param {Object} error 微信API错误
   * @param {Object} context 上下文信息
   */
  static handleWxApiError(error, context = {}) {
    const errorInfo = {
      type: 'WX_API_ERROR',
      message: this.getWxErrorMessage(error.errMsg),
      code: error.errno || error.errCode,
      api: context.api,
      timestamp: new Date().toISOString()
    };

    this.logError(errorInfo);
    this.reportError(errorInfo);
    this.showUserFeedback(errorInfo);

    return errorInfo;
  }

  /**
   * 处理未捕获的错误
   * @param {Error} error 错误对象
   */
  static handleUncaughtError(error) {
    const errorInfo = {
      type: 'UNCAUGHT_ERROR',
      message: error.message || '未知错误',
      stack: error.stack,
      timestamp: new Date().toISOString(),
      page: getCurrentPages().pop()?.route
    };

    this.logError(errorInfo);
    this.reportError(errorInfo);

    // 严重错误显示模态框
    wx.showModal({
      title: '应用异常',
      content: '应用遇到了意外错误，请重试或联系客服',
      confirmText: '重试',
      cancelText: '反馈问题',
      success: (res) => {
        if (res.confirm) {
          // 重新加载当前页面
          const currentPage = getCurrentPages().pop();
          if (currentPage && currentPage.onLoad) {
            currentPage.onLoad(currentPage.options);
          }
        } else {
          // 跳转到反馈页面
          wx.navigateTo({
            url: '/pages/profile-detail/help/help?type=error'
          });
        }
      }
    });

    return errorInfo;
  }

  /**
   * 记录错误日志
   * @param {Object} errorInfo 错误信息
   */
  static logError(errorInfo) {
    if (this.config.enableConsoleLog) {
      console.group('🚨 Error Handler');
      console.error('Type:', errorInfo.type);
      console.error('Message:', errorInfo.message);
      console.error('Code:', errorInfo.code);
      console.error('Timestamp:', errorInfo.timestamp);
      if (errorInfo.stack) {
        console.error('Stack:', errorInfo.stack);
      }
      console.groupEnd();
    }
  }

  /**
   * 上报错误信息
   * @param {Object} errorInfo 错误信息
   */
  static async reportError(errorInfo) {
    if (!this.config.enableErrorReport) return;

    try {
      const app = getApp();
      const globalData = app.globalData || {};
      
      const reportData = {
        ...errorInfo,
        userId: globalData.userInfo?.id,
        sessionId: globalData.sessionId,
        version: app.version || '1.0.0',
        platform: 'miniprogram',
        device: globalData.systemInfo
      };

      // 使用队列机制避免频繁上报
      this.addToReportQueue(reportData);
    } catch (reportError) {
      console.warn('错误上报失败:', reportError);
    }
  }

  /**
   * 显示用户反馈
   * @param {Object} errorInfo 错误信息
   */
  static showUserFeedback(errorInfo) {
    if (!this.config.showUserToast) return;

    const friendlyMessage = this.getFriendlyMessage(errorInfo);
    
    if (errorInfo.type === 'API_ERROR' && errorInfo.code >= 500) {
      // 服务器错误显示重试选项
      wx.showModal({
        title: '网络异常',
        content: friendlyMessage,
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 触发重试机制
            this.triggerRetry(errorInfo);
          }
        }
      });
    } else {
      wx.showToast({
        title: friendlyMessage,
        icon: 'error',
        duration: 2000
      });
    }
  }

  /**
   * 获取友好的错误消息
   * @param {Object} errorInfo 错误信息
   */
  static getFriendlyMessage(errorInfo) {
    const messageMap = {
      'API_ERROR': {
        401: '登录已过期，请重新登录',
        403: '权限不足，无法执行此操作',
        404: '请求的资源不存在',
        500: '服务器忙，请稍后重试',
        502: '服务器连接失败',
        503: '服务器维护中',
        timeout: '请求超时，请检查网络连接'
      },
      'BUSINESS_ERROR': errorInfo.message,
      'WX_API_ERROR': errorInfo.message,
      'UNCAUGHT_ERROR': '应用异常，请重试'
    };

    if (errorInfo.type === 'API_ERROR' && messageMap.API_ERROR[errorInfo.code]) {
      return messageMap.API_ERROR[errorInfo.code];
    }

    return messageMap[errorInfo.type] || errorInfo.message || '操作失败，请重试';
  }

  /**
   * 获取微信API错误消息
   * @param {string} errMsg 错误消息
   */
  static getWxErrorMessage(errMsg) {
    const wxErrorMap = {
      'request:fail': '网络请求失败',
      'request:timeout': '请求超时',
      'uploadFile:fail': '文件上传失败',
      'downloadFile:fail': '文件下载失败',
      'getLocation:fail': '获取位置信息失败',
      'chooseImage:fail': '选择图片失败',
      'authorize:fail': '授权失败'
    };

    for (const key in wxErrorMap) {
      if (errMsg.includes(key)) {
        return wxErrorMap[key];
      }
    }

    return '操作失败';
  }

  /**
   * 错误上报队列
   */
  static reportQueue = [];
  static isReporting = false;

  /**
   * 添加到上报队列
   * @param {Object} reportData 上报数据
   */
  static addToReportQueue(reportData) {
    this.reportQueue.push(reportData);
    
    if (!this.isReporting) {
      this.processReportQueue();
    }
  }

  /**
   * 处理上报队列
   */
  static async processReportQueue() {
    if (this.isReporting || this.reportQueue.length === 0) return;

    this.isReporting = true;

    try {
      while (this.reportQueue.length > 0) {
        const batch = this.reportQueue.splice(0, 10); // 批量上报，每次最多10条
        
        await new Promise((resolve) => {
          wx.request({
            url: this.config.errorReportUrl,
            method: 'POST',
            data: { errors: batch },
            success: () => resolve(),
            fail: () => resolve(), // 即使失败也继续处理
            complete: () => {
              // 避免过于频繁的请求
              setTimeout(resolve, 1000);
            }
          });
        });
      }
    } finally {
      this.isReporting = false;
    }
  }

  /**
   * 触发重试机制
   * @param {Object} errorInfo 错误信息
   */
  static triggerRetry(errorInfo) {
    const app = getApp();
    if (app.globalData && app.globalData.retryLastRequest) {
      app.globalData.retryLastRequest();
    }
  }

  /**
   * 设置配置
   * @param {Object} config 配置对象
   */
  static setConfig(config) {
    this.config = { ...this.config, ...config };
  }
}

module.exports = ErrorHandler;