/**
 * 智慧养鹅小程序 - 启动性能优化器
 * Startup Performance Optimizer for Smart Goose Mini-Program
 * 
 * 专注于小程序启动速度和首屏渲染优化
 */

class StartupOptimizer {
  constructor() {
    this.initTime = Date.now();
    this.performanceMetrics = {
      appLaunchTime: 0,
      firstScreenTime: 0,
      apiReadyTime: 0
    };
  }

  /**
   * 记录应用启动完成时间
   */
  markAppReady() {
    this.performanceMetrics.appLaunchTime = Date.now() - this.initTime;
    console.log(`[性能] 应用启动耗时: ${this.performanceMetrics.appLaunchTime}ms`);
  }

  /**
   * 记录首屏渲染完成时间
   */
  markFirstScreenReady() {
    this.performanceMetrics.firstScreenTime = Date.now() - this.initTime;
    console.log(`[性能] 首屏渲染耗时: ${this.performanceMetrics.firstScreenTime}ms`);
    
    // 如果首屏渲染时间超过3秒，记录警告
    if (this.performanceMetrics.firstScreenTime > 3000) {
      console.warn(`[性能警告] 首屏渲染时间过长: ${this.performanceMetrics.firstScreenTime}ms`);
    }
  }

  /**
   * 记录API就绪时间
   */
  markAPIReady() {
    this.performanceMetrics.apiReadyTime = Date.now() - this.initTime;
    console.log(`[性能] API就绪耗时: ${this.performanceMetrics.apiReadyTime}ms`);
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    return {
      ...this.performanceMetrics,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 预加载关键资源
   */
  preloadCriticalResources() {
    const criticalAPIs = [
      '/api/v1/user/profile',
      '/api/v1/home/<USER>'
    ];

    criticalAPIs.forEach(api => {
      this.prefetchAPI(api);
    });
  }

  /**
   * API预请求
   */
  async prefetchAPI(url) {
    try {
      const apiClient = require('./api-client-unified.js');
      await apiClient.get(url, { 
        timeout: 5000,
        silent: true // 静默请求，不显示loading
      });
    } catch (error) {
      console.warn(`[预加载] ${url} 预请求失败:`, error.message);
    }
  }

  /**
   * 图片预加载
   */
  preloadImages(imageUrls = []) {
    const defaultImages = [
      '/images/logo.png',
      '/images/default-avatar.png'
    ];

    [...defaultImages, ...imageUrls].forEach(url => {
      const image = wx.createImage ? wx.createImage() : new Image();
      image.src = url;
      image.onload = () => console.log(`[预加载] 图片加载完成: ${url}`);
      image.onerror = () => console.warn(`[预加载] 图片加载失败: ${url}`);
    });
  }
}

/**
 * 页面级别的性能优化器
 */
class PageOptimizer {
  constructor(pageName) {
    this.pageName = pageName;
    this.loadStartTime = Date.now();
    this.dataCache = new Map();
  }

  /**
   * setData 批处理优化
   */
  batchSetData(page, dataUpdates) {
    // 合并数据更新，减少setData调用次数
    const mergedData = {};
    
    if (Array.isArray(dataUpdates)) {
      dataUpdates.forEach(update => {
        Object.assign(mergedData, update);
      });
    } else {
      Object.assign(mergedData, dataUpdates);
    }

    // 使用防抖，避免频繁更新
    if (this.setDataTimeout) {
      clearTimeout(this.setDataTimeout);
    }

    this.setDataTimeout = setTimeout(() => {
      page.setData(mergedData);
      console.log(`[性能] 页面 ${this.pageName} setData 完成, 数据量: ${Object.keys(mergedData).length}`);
    }, 16); // 一帧的时间
  }

  /**
   * 智能数据缓存
   */
  getCachedData(key, fetchFunction, ttl = 300000) { // 默认5分钟缓存
    const cached = this.dataCache.get(key);
    
    if (cached && (Date.now() - cached.timestamp < ttl)) {
      console.log(`[缓存] 命中缓存: ${key}`);
      return Promise.resolve(cached.data);
    }

    console.log(`[缓存] 缓存未命中，重新获取: ${key}`);
    return fetchFunction().then(data => {
      this.dataCache.set(key, {
        data,
        timestamp: Date.now()
      });
      return data;
    });
  }

  /**
   * 列表数据虚拟滚动优化
   */
  optimizeListRendering(listData, visibleCount = 20) {
    if (listData.length <= visibleCount) {
      return listData;
    }

    // 只渲染可见区域的数据
    return {
      visibleItems: listData.slice(0, visibleCount),
      totalCount: listData.length,
      hasMore: true
    };
  }

  /**
   * 记录页面加载性能
   */
  recordLoadTime() {
    const loadTime = Date.now() - this.loadStartTime;
    console.log(`[性能] 页面 ${this.pageName} 加载耗时: ${loadTime}ms`);
    
    // 上报性能数据（可选）
    if (loadTime > 2000) {
      console.warn(`[性能警告] 页面 ${this.pageName} 加载时间过长: ${loadTime}ms`);
    }
    
    return loadTime;
  }
}

/**
 * 网络请求优化器
 */
class NetworkOptimizer {
  constructor() {
    this.requestQueue = new Map();
    this.maxConcurrent = 6; // 最大并发请求数
    this.currentRequests = 0;
  }

  /**
   * 请求去重
   */
  deduplicateRequest(url, options = {}) {
    const requestKey = `${url}:${JSON.stringify(options)}`;
    
    if (this.requestQueue.has(requestKey)) {
      console.log(`[网络优化] 请求去重: ${url}`);
      return this.requestQueue.get(requestKey);
    }

    const requestPromise = this.executeRequest(url, options);
    this.requestQueue.set(requestKey, requestPromise);
    
    // 请求完成后从队列中移除
    requestPromise.finally(() => {
      this.requestQueue.delete(requestKey);
    });

    return requestPromise;
  }

  /**
   * 并发请求控制
   */
  async controlledRequest(url, options = {}) {
    // 等待并发槽位
    while (this.currentRequests >= this.maxConcurrent) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    this.currentRequests++;
    
    try {
      return await this.executeRequest(url, options);
    } finally {
      this.currentRequests--;
    }
  }

  /**
   * 执行网络请求
   */
  executeRequest(url, options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url,
        ...options,
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 批量请求优化
   */
  async batchRequests(requests) {
    const results = [];
    const batchSize = 3; // 每批处理3个请求
    
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(request => this.controlledRequest(request.url, request.options))
      );
      results.push(...batchResults);
    }
    
    return results;
  }
}

// 创建全局单例
const startupOptimizer = new StartupOptimizer();
const networkOptimizer = new NetworkOptimizer();

/**
 * 性能优化工具函数
 */
const PerformanceUtils = {
  /**
   * 创建页面优化器
   */
  createPageOptimizer(pageName) {
    return new PageOptimizer(pageName);
  },

  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 图片懒加载
   */
  lazyLoadImage(selector, options = {}) {
    const observer = wx.createIntersectionObserver();
    
    observer.relativeToViewport().observe(selector, (res) => {
      if (res.intersectionRatio > 0) {
        // 图片进入可视区域，开始加载
        const imageSrc = options.dataSrc || res.dataset.src;
        if (imageSrc) {
          // 触发图片加载
          console.log(`[懒加载] 加载图片: ${imageSrc}`);
        }
      }
    });

    return observer;
  }
};

module.exports = {
  StartupOptimizer,
  PageOptimizer,
  NetworkOptimizer,
  PerformanceUtils,
  startupOptimizer,
  networkOptimizer
};