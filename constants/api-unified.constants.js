/**
 * 统一API常量配置 - 智慧养鹅SAAS平台
 * 基于微信小程序RESTful设计规范
 * 
 * 设计原则：
 * 1. 统一版本控制 - 使用 /api/v1
 * 2. 资源导向 - URL使用名词，HTTP动词表示操作
 * 3. 层级清晰 - 按业务模块组织，层级不超过3级
 * 4. 命名规范 - 小写字母和连字符，复数形式表示集合
 * 5. 一致性 - 相同操作在不同资源上保持相同的URL模式
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  TESTING: 'testing', 
  STAGING: 'staging',
  PRODUCTION: 'production'
};

// 当前环境
const CURRENT_ENV = ENV.DEVELOPMENT;

// 环境对应的API基础地址
const BASE_URLS = {
  [ENV.DEVELOPMENT]: 'http://localhost:3001',
  [ENV.TESTING]: 'https://test-api.zhihuiyange.com',
  [ENV.STAGING]: 'https://staging-api.zhihuiyange.com',
  [ENV.PRODUCTION]: 'https://api.zhihuiyange.com'
};

// 统一API版本
const API_VERSION = '/api/v2';

// 获取当前环境的基础URL
const BASE_URL = BASE_URLS[CURRENT_ENV];

// 构建完整API路径的辅助函数
const buildApiUrl = (path) => `${BASE_URL}${API_VERSION}${path}`;

/**
 * 统一API端点配置
 * 按业务模块组织，遵循RESTful规范
 */
const API_ENDPOINTS = {
  
  // ===== 认证与用户管理 =====
  AUTH: {
    // 认证相关
    LOGIN: buildApiUrl('/auth/login'),
    LOGOUT: buildApiUrl('/auth/logout'),
    REFRESH: buildApiUrl('/auth/refresh'),
    REGISTER: buildApiUrl('/auth/register'),
    WECHAT_LOGIN: buildApiUrl('/auth/wechat'),
    VERIFY_CODE: buildApiUrl('/auth/verify-code'),
    RESET_PASSWORD: buildApiUrl('/auth/reset-password'),
    
    // 用户信息
    PROFILE: buildApiUrl('/auth/profile'),
    USER_INFO: buildApiUrl('/auth/userinfo'), // 向后兼容
    CHANGE_PASSWORD: buildApiUrl('/auth/password')
  },

  // 用户管理
  USERS: {
    LIST: buildApiUrl('/users'),
    DETAIL: (id) => buildApiUrl(`/users/${id}`),
    CREATE: buildApiUrl('/users'),
    UPDATE: (id) => buildApiUrl(`/users/${id}`),
    DELETE: (id) => buildApiUrl(`/users/${id}`),
    PROFILE: (id) => buildApiUrl(`/users/${id}/profile`),
    PERMISSIONS: (id) => buildApiUrl(`/users/${id}/permissions`),
    ROLES: (id) => buildApiUrl(`/users/${id}/roles`)
  },

  // ===== 核心业务模块 =====
  
  // 鹅群管理
  FLOCKS: {
    LIST: buildApiUrl('/flocks'),
    DETAIL: (id) => buildApiUrl(`/flocks/${id}`),
    CREATE: buildApiUrl('/flocks'),
    UPDATE: (id) => buildApiUrl(`/flocks/${id}`),
    DELETE: (id) => buildApiUrl(`/flocks/${id}`),
    STATISTICS: (id) => buildApiUrl(`/flocks/${id}/statistics`),
    BATCH: buildApiUrl('/flocks/batch')
  },

  // 生产管理（包含健康监测）
  PRODUCTION: {
    RECORDS: buildApiUrl('/production/records'),
    RECORD_DETAIL: (id) => buildApiUrl(`/production/records/${id}`),
    CREATE_RECORD: buildApiUrl('/production/records'),
    UPDATE_RECORD: (id) => buildApiUrl(`/production/records/${id}`),
    DELETE_RECORD: (id) => buildApiUrl(`/production/records/${id}`),
    STATISTICS: buildApiUrl('/production/statistics'),
    KNOWLEDGE: buildApiUrl('/production/knowledge'),
    AI_DIAGNOSIS: buildApiUrl('/production/ai-diagnosis'),
    REPORTS: buildApiUrl('/production/reports')
  },

  // 库存管理
  INVENTORY: {
    ITEMS: buildApiUrl('/inventory/items'),
    ITEM_DETAIL: (id) => buildApiUrl(`/inventory/items/${id}`),
    CREATE_ITEM: buildApiUrl('/inventory/items'),
    UPDATE_ITEM: (id) => buildApiUrl(`/inventory/items/${id}`),
    DELETE_ITEM: (id) => buildApiUrl(`/inventory/items/${id}`),
    CATEGORIES: buildApiUrl('/inventory/categories'),
    LOW_STOCK: buildApiUrl('/inventory/low-stock'),
    BATCH_UPDATE: buildApiUrl('/inventory/batch')
  },

  // ===== 工作台系统 =====
  WORKSPACE: {
    // 工作台首页
    DASHBOARD: buildApiUrl('/workspace/dashboard'),
    STATISTICS: buildApiUrl('/workspace/statistics'),

    // 申请管理
    APPLICATIONS: {
      LIST: buildApiUrl('/workspace/applications'),
      DETAIL: (id) => buildApiUrl(`/workspace/applications/${id}`),
      CREATE: buildApiUrl('/workspace/applications'),
      UPDATE: (id) => buildApiUrl(`/workspace/applications/${id}`),
      SUBMIT: (id) => buildApiUrl(`/workspace/applications/${id}/submit`),
      CANCEL: (id) => buildApiUrl(`/workspace/applications/${id}/cancel`),
      APPROVE: (id) => buildApiUrl(`/workspace/applications/${id}/approve`)
    },

    // 采购管理 - 工作台模块
    PURCHASE: {
      LIST: buildApiUrl('/oa/purchase/requests'),
      DETAIL: (id) => buildApiUrl(`/oa/purchase/requests/${id}`),
      CREATE: buildApiUrl('/oa/purchase/requests'),
      UPDATE: (id) => buildApiUrl(`/oa/purchase/requests/${id}`),
      DELETE: (id) => buildApiUrl(`/oa/purchase/requests/${id}`),
      CANCEL: (id) => buildApiUrl(`/oa/purchase/requests/${id}/cancel`),
      STATISTICS: buildApiUrl('/oa/purchase/statistics'),
      MY_REQUESTS: buildApiUrl('/oa/purchase/my-requests'),
      PENDING_APPROVALS: buildApiUrl('/oa/purchase/pending-approvals'),
      APPROVE: (id) => buildApiUrl(`/oa/purchase/requests/${id}/approve`),
      SUPPLIERS: buildApiUrl('/oa/purchase/suppliers')
    },

    // 审批管理
    APPROVALS: {
      PENDING: buildApiUrl('/workspace/approvals/pending'),
      HISTORY: buildApiUrl('/workspace/approvals/history'),
      BATCH: buildApiUrl('/workspace/approvals/batch'),
      STATISTICS: buildApiUrl('/workspace/approvals/statistics')
    },

    // 财务管理
    FINANCE: {
      OVERVIEW: buildApiUrl('/workspace/finance/overview'),
      STATISTICS: buildApiUrl('/workspace/finance/statistics'),
      PERSONAL_SUMMARY: buildApiUrl('/workspace/finance/personal-summary'),
      RECENT_RECORDS: buildApiUrl('/workspace/finance/recent-records'),
      MONTHLY_TREND: buildApiUrl('/workspace/finance/monthly-trend'),
      REPORTS: {
        OVERVIEW: buildApiUrl('/workspace/finance/reports/overview'),
        CATEGORY_STATS: buildApiUrl('/workspace/finance/reports/category-stats'),
        TREND: buildApiUrl('/workspace/finance/reports/trend'),
        COMPARISON: buildApiUrl('/workspace/finance/reports/comparison'),
        EXPORT_EXCEL: buildApiUrl('/workspace/finance/reports/export/excel'),
        EXPORT_PDF: buildApiUrl('/workspace/finance/reports/export/pdf')
      },
      EXPORT: buildApiUrl('/workspace/finance/export')
    },

    // 向后兼容
    PENDING_APPROVALS: buildApiUrl('/workspace/approvals/pending'),
    MY_APPROVALS: buildApiUrl('/workspace/approvals/history'),
    FINANCE_RECORDS: buildApiUrl('/workspace/finance/recent-records'),
    REPORTS: buildApiUrl('/workspace/finance/reports'),
    BATCH_APPROVE: buildApiUrl('/workspace/approvals/batch')
  },

  // ===== OA办公自动化系统 (保留兼容性) =====
  OA: {
    // 统计概览
    STATISTICS: buildApiUrl('/oa/statistics'),
    ACTIVITIES: buildApiUrl('/oa/activities'),





    // 报销管理
    REIMBURSEMENT: {
      REQUESTS: buildApiUrl('/oa/reimbursement/requests'),
      REQUEST_DETAIL: (id) => buildApiUrl(`/oa/reimbursement/requests/${id}`),
      CREATE_REQUEST: buildApiUrl('/oa/reimbursement/requests'),
      UPDATE_REQUEST: (id) => buildApiUrl(`/oa/reimbursement/requests/${id}`),
      SUBMIT_REQUEST: (id) => buildApiUrl(`/oa/reimbursement/requests/${id}/status`),
      STATISTICS: buildApiUrl('/oa/reimbursement/statistics')
    },

    // 采购管理 - 重新集成到财务管理模块
    PURCHASE: {
      LIST: buildApiUrl('/oa/purchase/requests'),
      DETAIL: buildApiUrl('/oa/purchase/requests'),
      CREATE: buildApiUrl('/oa/purchase/requests'),
      UPDATE: buildApiUrl('/oa/purchase/requests'),
      DELETE: buildApiUrl('/oa/purchase/requests'),
      CANCEL: buildApiUrl('/oa/purchase/requests'),
      STATISTICS: buildApiUrl('/oa/purchase/statistics'),
      MY_REQUESTS: buildApiUrl('/oa/purchase/my-requests'),
      PENDING_APPROVALS: buildApiUrl('/oa/purchase/pending-approvals'),
      APPROVE: buildApiUrl('/oa/purchase/approve'),
      SUPPLIERS: buildApiUrl('/oa/purchase/suppliers')
    },

    // 审批流程
    APPROVALS: {
      PENDING: buildApiUrl('/oa/approvals/pending'),
      URGENT: buildApiUrl('/oa/approvals/urgent'),
      HISTORY: buildApiUrl('/oa/approvals/history'),
      STATISTICS: buildApiUrl('/oa/approvals/statistics'),
      PROCESS: (id) => buildApiUrl(`/oa/approvals/${id}/process`)
    },

    // 财务管理
    FINANCE: {
      OVERVIEW: buildApiUrl('/oa/finance/overview'),
      REPORTS: buildApiUrl('/oa/finance/reports'),
      STATISTICS: buildApiUrl('/oa/finance/statistics'),
      STATS: buildApiUrl('/oa/finance/statistics'),
      MY_REIMBURSEMENT_STATS: buildApiUrl('/oa/finance/my-reimbursement-stats'),
      ALL_RECORDS: buildApiUrl('/oa/finance/all-records'),
      MY_REIMBURSEMENTS: buildApiUrl('/oa/finance/my-reimbursements'),
      ACTIVITIES: buildApiUrl('/oa/finance/activities'),
      ALERTS: buildApiUrl('/oa/finance/alerts'),
      EXPORT: buildApiUrl('/oa/finance/export')
    },

    // 员工管理
    STAFF: {
      LIST: buildApiUrl('/oa/staff/list'),
      STATS: buildApiUrl('/oa/staff/statistics'),
      DETAIL: (id) => buildApiUrl(`/oa/staff/${id}`),
      CREATE: buildApiUrl('/oa/staff'),
      UPDATE: (id) => buildApiUrl(`/oa/staff/${id}`),
      DELETE: (id) => buildApiUrl(`/oa/staff/${id}`),
      PERMISSIONS: (id) => buildApiUrl(`/oa/staff/${id}/permissions`)
    },

    // 工作流配置
    WORKFLOWS: {
      TEMPLATES: buildApiUrl('/oa/workflows/templates'),
      TEMPLATE_DETAIL: (id) => buildApiUrl(`/oa/workflows/templates/${id}`),
      CREATE_TEMPLATE: buildApiUrl('/oa/workflows/templates'),
      UPDATE_TEMPLATE: (id) => buildApiUrl(`/oa/workflows/templates/${id}`),
      DELETE_TEMPLATE: (id) => buildApiUrl(`/oa/workflows/templates/${id}`),
      COPY_TEMPLATE: (id) => buildApiUrl(`/oa/workflows/templates/${id}/copy`),
      STATISTICS: buildApiUrl('/oa/workflows/statistics')
    },

    // 权限管理
    PERMISSIONS: {
      USERS: buildApiUrl('/oa/permissions/users'),
      USER_DETAIL: (id) => buildApiUrl(`/oa/permissions/users/${id}`),
      USER_ROLES: (id) => buildApiUrl(`/oa/permissions/users/${id}/roles`),
      ROLES: buildApiUrl('/oa/permissions/roles'),
      ROLE_DETAIL: (id) => buildApiUrl(`/oa/permissions/roles/${id}`),
      ROLE_PERMISSIONS: (id) => buildApiUrl(`/oa/permissions/roles/${id}/permissions`),
      DEPARTMENTS: buildApiUrl('/oa/permissions/departments'),
      STATISTICS: buildApiUrl('/oa/permissions/statistics')
    }
  },

  // ===== 商城系统 =====
  SHOP: {
    // 商品管理
    PRODUCTS: buildApiUrl('/shop/products'),
    PRODUCT_DETAIL: (id) => buildApiUrl(`/shop/products/${id}`),
    CATEGORIES: buildApiUrl('/shop/products/categories'),
    
    // 购物车
    CART: buildApiUrl('/shop/cart'),
    CART_ITEMS: buildApiUrl('/shop/cart/items'),
    CART_ITEM: (id) => buildApiUrl(`/shop/cart/items/${id}`),
    
    // 订单管理
    ORDERS: buildApiUrl('/shop/orders'),
    ORDER_DETAIL: (id) => buildApiUrl(`/shop/orders/${id}`),
    CREATE_ORDER: buildApiUrl('/shop/orders'),
    CANCEL_ORDER: (id) => buildApiUrl(`/shop/orders/${id}/status`),
    
    // 支付相关
    PAYMENT: buildApiUrl('/shop/payment'),
    PAYMENT_NOTIFY: buildApiUrl('/shop/payment/notify')
  },

  // ===== 系统管理 =====
  
  // 首页数据
  HOME: {
    STATISTICS: buildApiUrl('/home/<USER>'),
    ANNOUNCEMENTS: buildApiUrl('/home/<USER>'),
    WEATHER: buildApiUrl('/home/<USER>'),
    QUICK_ACTIONS: buildApiUrl('/home/<USER>')
  },

  // 通知系统
  NOTIFICATIONS: {
    LIST: buildApiUrl('/notifications'),
    UNREAD_COUNT: buildApiUrl('/notifications/unread-count'),
    MARK_READ: (id) => buildApiUrl(`/notifications/${id}/read`),
    MARK_ALL_READ: buildApiUrl('/notifications/read-all')
  },

  // 帮助中心
  HELP: {
    ARTICLES: buildApiUrl('/help/articles'),
    ARTICLE_DETAIL: (id) => buildApiUrl(`/help/articles/${id}`),
    CATEGORIES: buildApiUrl('/help/categories'),
    SEARCH: buildApiUrl('/help/search'),
    FAQ: buildApiUrl('/help/faq')
  },

  // 系统设置
  SETTINGS: {
    SYSTEM: buildApiUrl('/settings/system'),
    VERSION: buildApiUrl('/settings/version'),
    CONFIG: buildApiUrl('/settings/config')
  },

  // 文件上传
  UPLOAD: {
    IMAGE: buildApiUrl('/upload/image'),
    FILE: buildApiUrl('/upload/file'),
    BATCH: buildApiUrl('/upload/batch')
  },

  // ===== 租户管理 (多租户SAAS) =====
  TENANTS: {
    LIST: buildApiUrl('/tenants'),
    DETAIL: (id) => buildApiUrl(`/tenants/${id}`),
    CREATE: buildApiUrl('/tenants'),
    UPDATE: (id) => buildApiUrl(`/tenants/${id}`),
    DELETE: (id) => buildApiUrl(`/tenants/${id}`),
    ACTIVATE: (id) => buildApiUrl(`/tenants/${id}/activate`),
    SUSPEND: (id) => buildApiUrl(`/tenants/${id}/suspend`),
    STATISTICS: buildApiUrl('/tenants/statistics'),
    
    // 租户数据
    DATA: (id) => buildApiUrl(`/tenants/${id}/data`),
    BUSINESS: (id) => buildApiUrl(`/tenants/${id}/business`),
    USERS: (id) => buildApiUrl(`/tenants/${id}/users`)
  },

  // ===== 平台管理 (超级管理员) =====
  ADMIN: {
    // 平台统计
    STATISTICS: buildApiUrl('/admin/statistics'),
    ANALYTICS: buildApiUrl('/admin/analytics'),
    REVENUE: buildApiUrl('/admin/revenue'),
    USAGE: buildApiUrl('/admin/usage'),
    
    // 系统监控
    HEALTH: buildApiUrl('/admin/health'),
    LOGS: buildApiUrl('/admin/logs'),
    ERRORS: buildApiUrl('/admin/errors'),
    
    // 订阅管理
    SUBSCRIPTIONS: buildApiUrl('/admin/subscriptions'),
    PLANS: buildApiUrl('/admin/plans'),
    BILLING: buildApiUrl('/admin/billing')
  }
};

// HTTP状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
};

// HTTP方法
const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
};

// 请求头常量
const HEADERS = {
  CONTENT_TYPE: {
    JSON: 'application/json',
    FORM: 'application/x-www-form-urlencoded',
    MULTIPART: 'multipart/form-data'
  },
  AUTHORIZATION: 'Authorization',
  TENANT_ID: 'X-Tenant-ID',
  CLIENT_VERSION: 'X-Client-Version',
  PLATFORM: 'X-Platform',
  REQUEST_ID: 'X-Request-ID'
};

// 错误类型
const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR'
};

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_TYPES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_TYPES.SERVER_ERROR]: '服务器异常，请稍后重试',
  [ERROR_TYPES.AUTH_ERROR]: '登录已过期，请重新登录',
  [ERROR_TYPES.BUSINESS_ERROR]: '操作失败，请稍后重试',
  [ERROR_TYPES.VALIDATION_ERROR]: '输入数据有误，请检查后重试'
};

// 请求配置常量
const REQUEST_CONFIG = {
  TIMEOUT: 10000,           // 请求超时时间(ms)
  RETRY_COUNT: 2,           // 重试次数
  RETRY_DELAY: 1000,        // 重试延迟(ms)
  MAX_CONCURRENT: 5,        // 最大并发请求数
  CACHE_TIME: 300000        // 缓存时间(ms) - 5分钟
};

// 导出统一API配置
module.exports = {
  ENV,
  CURRENT_ENV,
  BASE_URLS,
  BASE_URL,
  API_VERSION,
  API_ENDPOINTS,
  HTTP_STATUS,
  HTTP_METHODS,
  HEADERS,
  ERROR_TYPES,
  ERROR_MESSAGES,
  REQUEST_CONFIG,
  
  // 工具函数
  buildApiUrl,
  
  // 向后兼容（逐步迁移时使用）
  ENDPOINTS: API_ENDPOINTS
};