<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .inventory-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #6f42c1;
        }
        .alert-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .alert-item.warning {
            border-left: 4px solid #ffc107;
        }
        .alert-item.danger {
            border-left: 4px solid #dc3545;
        }
        .alert-item.info {
            border-left: 4px solid #17a2b8;
        }
        .category-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border-left: 4px solid #28a745;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            margin-right: 15px;
        }
        .feed-bg { background: linear-gradient(135deg, #28a745, #20c997); }
        .medicine-bg { background: linear-gradient(135deg, #dc3545, #e83e8c); }
        .equipment-bg { background: linear-gradient(135deg, #6f42c1, #6610f2); }
        .materials-bg { background: linear-gradient(135deg, #fd7e14, #ffc107); }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>
    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0" id="page-title">库存预警配置</h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>
                
                <div class="container-fluid mt-4">
                    <!-- 页面标题和操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="bi bi-exclamation-triangle me-2"></i>库存预警配置</h1>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" data-bs-toggle="modal" data-bs-target="#historyModal">
                                <i class="bi bi-clock-history"></i> 预警历史
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" data-bs-toggle="modal" data-bs-target="#categoryModal">
                                <i class="bi bi-plus"></i> 添加类别
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAlertModal">
                                <i class="bi bi-plus"></i> 添加预警
                            </button>
                        </div>
                    </div>
                    
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="inventory-card text-center">
                                <h6 class="text-muted">预警总数</h6>
                                <h3 class="text-primary"><%= alertStats.totalAlerts %></h3>
                                <small class="text-muted">个预警</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="inventory-card text-center">
                                <h6 class="text-muted">启用预警</h6>
                                <h3 class="text-success"><%= alertStats.activeAlerts %></h3>
                                <small class="text-muted">个启用</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="inventory-card text-center">
                                <h6 class="text-muted">今日触发</h6>
                                <h3 class="text-warning"><%= alertStats.triggeredToday %></h3>
                                <small class="text-muted">次触发</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="inventory-card text-center">
                                <h6 class="text-muted">累计触发</h6>
                                <h3 class="text-info"><%= alertStats.totalTriggered %></h3>
                                <small class="text-muted">次触发</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="inventory-card text-center">
                                <h6 class="text-muted">紧急预警</h6>
                                <h3 class="text-danger"><%= alertStats.criticalAlerts %></h3>
                                <small class="text-muted">个紧急</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="inventory-card text-center">
                                <h6 class="text-muted">今日处理</h6>
                                <h3 class="text-secondary"><%= alertStats.resolvedToday %></h3>
                                <small class="text-muted">个处理</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 预警配置列表 -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-bell me-2"></i>预警配置列表
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <% inventoryAlerts.forEach(function(alert) { %>
                                        <div class="alert-item <%= alert.severity %>">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h6 class="mb-0">
                                                            <%= alert.alertName %>
                                                            <span class="badge <%= alert.isActive ? 'bg-success' : 'bg-secondary' %> ms-2">
                                                                <%= alert.isActive ? '启用' : '停用' %>
                                                            </span>
                                                            <span class="badge bg-<%= alert.severity === 'danger' ? 'danger' : alert.severity === 'warning' ? 'warning' : 'info' %> ms-1">
                                                                <%= alert.severity === 'danger' ? '危险' : alert.severity === 'warning' ? '警告' : '信息' %>
                                                            </span>
                                                        </h6>
                                                    </div>
                                                    <p class="text-muted mb-2"><%= alert.message %></p>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <small class="text-muted">
                                                                类别: <strong><%= alert.category === 'all' ? '全部' : alert.category %></strong>
                                                            </small>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <small class="text-muted">
                                                                阈值: <strong><%= alert.threshold %><%= alert.unit %></strong>
                                                            </small>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <small class="text-muted">
                                                                触发: <strong><%= alert.triggeredCount %>次</strong>
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2">
                                                        <small class="text-muted">
                                                            通知方式: 
                                                            <% alert.notificationMethods.forEach(function(method, index) { %>
                                                                <%= index > 0 ? ', ' : '' %><%= method === 'email' ? '邮件' : method === 'sms' ? '短信' : '系统' %>
                                                            <% }) %>
                                                            | 目标: <%= alert.targetTenants === 'all' ? '所有租户' : '指定租户' %>
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="ms-3">
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="testAlert(<%= alert.id %>)">
                                                            <i class="bi bi-play"></i> 测试
                                                        </button>
                                                        <button class="btn btn-outline-secondary" onclick="editAlert(<%= alert.id %>)">
                                                            <i class="bi bi-pencil"></i> 编辑
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="deleteAlert(<%= alert.id %>)">
                                                            <i class="bi bi-trash"></i> 删除
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <% }) %>
                                </div>
                            </div>
                        </div>

                        <!-- 类别配置 -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-tags me-2"></i>库存类别配置
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <% categoryConfig.forEach(function(category) { %>
                                        <div class="category-item">
                                            <div class="d-flex align-items-center">
                                                <div class="category-icon <%= category.category %>-bg">
                                                    <i class="bi bi-box"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <%= category.name %>
                                                        <span class="badge <%= category.isActive ? 'bg-success' : 'bg-secondary' %> ms-2">
                                                            <%= category.isActive ? '启用' : '停用' %>
                                                        </span>
                                                    </h6>
                                                    <small class="text-muted">
                                                        最小库存: <%= category.minStockDefault %> | 
                                                        最大库存: <%= category.maxStockDefault %>
                                                    </small>
                                                </div>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <button class="btn btn-outline-secondary" onclick="editCategory('<%= category.category %>')">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <% }) %>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加预警配置模态框 -->
    <div class="modal fade" id="addAlertModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加库存预警配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAlertForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">预警名称</label>
                                <input type="text" class="form-control" name="alertName" placeholder="如: 饲料库存预警" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">库存类别</label>
                                <select class="form-select" name="category" required>
                                    <option value="">选择类别</option>
                                    <option value="feed">饲料</option>
                                    <option value="medicine">药品</option>
                                    <option value="equipment">设备</option>
                                    <option value="materials">物料</option>
                                    <option value="all">全部类别</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">预警类型</label>
                                <select class="form-select" name="alertType" required>
                                    <option value="">选择类型</option>
                                    <option value="low_stock">库存不足</option>
                                    <option value="expiry">即将过期</option>
                                    <option value="maintenance">维护提醒</option>
                                    <option value="abnormal_change">异常变动</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">阈值</label>
                                <input type="number" class="form-control" name="threshold" placeholder="如: 10" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">单位</label>
                                <select class="form-select" name="unit">
                                    <option value="个">个</option>
                                    <option value="袋">袋</option>
                                    <option value="盒">盒</option>
                                    <option value="天">天</option>
                                    <option value="%">%</option>
                                    <option value="kg">kg</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">严重程度</label>
                                <select class="form-select" name="severity" required>
                                    <option value="">选择程度</option>
                                    <option value="info">信息</option>
                                    <option value="warning">警告</option>
                                    <option value="danger">危险</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">目标租户</label>
                                <select class="form-select" name="targetTenants">
                                    <option value="all">所有租户</option>
                                    <option value="specific">指定租户</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">预警消息</label>
                            <textarea class="form-control" name="message" rows="3" placeholder="预警触发时发送的消息内容" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">通知方式</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="notificationMethods" value="system" id="system" checked>
                                        <label class="form-check-label" for="system">系统通知</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="notificationMethods" value="email" id="email">
                                        <label class="form-check-label" for="email">邮件通知</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="notificationMethods" value="sms" id="sms">
                                        <label class="form-check-label" for="sms">短信通知</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="isActive" id="alertIsActive" checked>
                            <label class="form-check-label" for="alertIsActive">启用预警</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveAlert()">保存预警</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试预警规则
        function testAlert(alertId) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            button.disabled = true;
            
            fetch(`/saas-admin/inventory/alerts/${alertId}/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`预警规则测试成功！\n匹配租户: ${data.data.matchedTenants}个\n预计通知: ${data.data.estimatedNotifications}条\n测试触发: ${data.data.testData.triggered ? '是' : '否'}`);
                } else {
                    alert('测试失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('测试失败: ' + error.message);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        // 保存预警配置
        function saveAlert() {
            const form = document.getElementById('addAlertForm');
            const formData = new FormData(form);
            
            // 处理多选框
            const notificationMethods = [];
            form.querySelectorAll('input[name="notificationMethods"]:checked').forEach(checkbox => {
                notificationMethods.push(checkbox.value);
            });
            
            const data = {
                alertName: formData.get('alertName'),
                category: formData.get('category'),
                alertType: formData.get('alertType'),
                threshold: formData.get('threshold'),
                unit: formData.get('unit'),
                severity: formData.get('severity'),
                targetTenants: formData.get('targetTenants'),
                message: formData.get('message'),
                notificationMethods: notificationMethods,
                isActive: formData.get('isActive') === 'on'
            };
            
            fetch('/saas-admin/inventory/alerts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('预警配置保存成功！');
                    location.reload();
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('保存失败: ' + error.message);
            });
        }
        
        // 删除预警配置
        function deleteAlert(alertId) {
            if (confirm('确定要删除这个预警配置吗？')) {
                fetch(`/saas-admin/inventory/alerts/${alertId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('预警配置删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }
        
        // 编辑预警配置（简化实现）
        function editAlert(alertId) {
            alert('编辑功能开发中...');
        }
        
        // 编辑类别配置（简化实现）
        function editCategory(category) {
            alert('编辑类别功能开发中...');
        }
    </script>
</body>
</html>