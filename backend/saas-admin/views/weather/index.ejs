<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅SaaS平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .weather-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #17a2b8;
        }
        .config-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .config-item.active {
            border-left: 4px solid #28a745;
        }
        .config-item.inactive {
            border-left: 4px solid #6c757d;
        }
        .alert-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .alert-item.warning {
            border-left: 4px solid #ffc107;
        }
        .alert-item.danger {
            border-left: 4px solid #dc3545;
        }
        .provider-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            margin-right: 15px;
        }
        .qweather-bg { background: linear-gradient(135deg, #4285f4, #3367d6); }
        .amap-bg { background: linear-gradient(135deg, #ff6900, #e55a00); }
    </style>
</head>
<body>
    <%- include('../partials/navbar') %>
    <div class="container-fluid">
        <div class="row">
            <%- include('../partials/sidebar') %>
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <h5 class="mb-0" id="page-title">天气配置管理</h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-3">在线</span>
                            <i class="bi bi-bell fs-5 me-3"></i>
                            <i class="bi bi-gear fs-5"></i>
                        </div>
                    </div>
                </nav>
                
                <div class="container-fluid mt-4">
                    <!-- 页面标题和操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2"><i class="bi bi-cloud-sun me-2"></i>天气配置管理</h1>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" data-bs-toggle="modal" data-bs-target="#reportModal">
                                <i class="bi bi-bar-chart"></i> 统计报告
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" data-bs-toggle="modal" data-bs-target="#addAlertModal">
                                <i class="bi bi-plus"></i> 添加预警
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addConfigModal">
                                <i class="bi bi-plus"></i> 添加API配置
                            </button>
                        </div>
                    </div>
                    
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="weather-card text-center">
                                <h6 class="text-muted">API调用总数</h6>
                                <h3 class="text-info"><%= stats.totalApiCalls.toLocaleString() %></h3>
                                <small class="text-muted">次调用</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="weather-card text-center">
                                <h6 class="text-muted">今日调用</h6>
                                <h3 class="text-success"><%= stats.todayApiCalls %></h3>
                                <small class="text-muted">次调用</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="weather-card text-center">
                                <h6 class="text-muted">活跃预警</h6>
                                <h3 class="text-warning"><%= stats.activeAlerts %></h3>
                                <small class="text-muted">个预警</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="weather-card text-center">
                                <h6 class="text-muted">覆盖租户</h6>
                                <h3 class="text-primary"><%= stats.coveredTenants %></h3>
                                <small class="text-muted">个租户</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="weather-card text-center">
                                <h6 class="text-muted">平均响应时间</h6>
                                <h3 class="text-info"><%= stats.averageResponseTime %></h3>
                                <small class="text-muted">响应时间</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="weather-card text-center">
                                <h6 class="text-muted">成功率</h6>
                                <h3 class="text-success"><%= stats.successRate %></h3>
                                <small class="text-muted">成功率</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 天气API配置 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-cloud-arrow-down me-2"></i>天气API配置
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <% weatherConfig.forEach(function(config) { %>
                                        <div class="config-item <%= config.isActive ? 'active' : 'inactive' %>">
                                            <div class="d-flex align-items-center">
                                                <div class="provider-icon <%= config.provider %>-bg">
                                                    <i class="bi bi-cloud"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <h6 class="mb-1">
                                                                <%= config.name %>
                                                                <% if (config.isDefault) { %>
                                                                    <span class="badge bg-primary ms-2">默认</span>
                                                                <% } %>
                                                                <span class="badge <%= config.isActive ? 'bg-success' : 'bg-secondary' %> ms-1">
                                                                    <%= config.isActive ? '活跃' : '停用' %>
                                                                </span>
                                                            </h6>
                                                            <p class="text-muted mb-1">提供商: <%= config.provider %></p>
                                                            <small class="text-muted">
                                                                更新间隔: <%= config.updateInterval %>分钟 | 
                                                                功能: <%= config.features.join(', ') %>
                                                            </small>
                                                        </div>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" onclick="testWeatherAPI(<%= config.id %>)">
                                                                <i class="bi bi-wifi"></i> 测试
                                                            </button>
                                                            <button class="btn btn-outline-secondary" onclick="editConfig(<%= config.id %>)">
                                                                <i class="bi bi-pencil"></i> 编辑
                                                            </button>
                                                            <button class="btn btn-outline-danger" onclick="deleteConfig(<%= config.id %>)">
                                                                <i class="bi bi-trash"></i> 删除
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <% }) %>
                                </div>
                            </div>
                        </div>

                        <!-- 天气预警配置 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-exclamation-triangle me-2"></i>天气预警配置
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <% alertConfig.forEach(function(alert) { %>
                                        <div class="alert-item <%= alert.severity %>">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <%= alert.alertName %>
                                                        <span class="badge <%= alert.isActive ? 'bg-success' : 'bg-secondary' %> ms-2">
                                                            <%= alert.isActive ? '启用' : '停用' %>
                                                        </span>
                                                        <span class="badge bg-<%= alert.severity === 'danger' ? 'danger' : 'warning' %> ms-1">
                                                            <%= alert.severity === 'danger' ? '危险' : '警告' %>
                                                        </span>
                                                    </h6>
                                                    <p class="text-muted mb-1"><%= alert.message %></p>
                                                    <small class="text-muted">
                                                        <% if (alert.threshold) { %>
                                                            条件: <%= alert.alertType %> <%= alert.operator %> <%= alert.threshold %>
                                                        <% } else if (alert.condition) { %>
                                                            条件: <%= alert.condition.join(', ') %>
                                                        <% } %>
                                                        | 目标: <%= alert.targetTenants === 'all' ? '所有租户' : '指定租户' %>
                                                    </small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-secondary" onclick="editAlert(<%= alert.id %>)">
                                                        <i class="bi bi-pencil"></i> 编辑
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="deleteAlert(<%= alert.id %>)">
                                                        <i class="bi bi-trash"></i> 删除
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <% }) %>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加API配置模态框 -->
    <div class="modal fade" id="addConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加天气API配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addConfigForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">配置名称</label>
                                <input type="text" class="form-control" name="name" placeholder="如: 和风天气API" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">服务提供商</label>
                                <select class="form-select" name="provider" required>
                                    <option value="">选择提供商</option>
                                    <option value="qweather">和风天气</option>
                                    <option value="amap">高德天气</option>
                                    <option value="openweather">OpenWeather</option>
                                    <option value="caiyun">彩云天气</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">API密钥</label>
                            <input type="password" class="form-control" name="apiKey" placeholder="输入API密钥" required>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">更新间隔(分钟)</label>
                                <input type="number" class="form-control" name="updateInterval" value="30" min="5" max="1440">
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="isDefault" id="isDefault">
                                    <label class="form-check-label" for="isDefault">设为默认</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="isActive" id="isActive" checked>
                                    <label class="form-check-label" for="isActive">启用配置</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">支持功能</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="features" value="current" id="current" checked>
                                        <label class="form-check-label" for="current">实时天气</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="features" value="forecast" id="forecast" checked>
                                        <label class="form-check-label" for="forecast">天气预报</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="features" value="alerts" id="alerts">
                                        <label class="form-check-label" for="alerts">天气预警</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveWeatherConfig()">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加预警配置模态框 -->
    <div class="modal fade" id="addAlertModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加天气预警配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAlertForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">预警名称</label>
                                <input type="text" class="form-control" name="alertName" placeholder="如: 高温预警" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">预警类型</label>
                                <select class="form-select" name="alertType" required>
                                    <option value="">选择类型</option>
                                    <option value="temperature">温度</option>
                                    <option value="humidity">湿度</option>
                                    <option value="weather">天气现象</option>
                                    <option value="air_quality">空气质量</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">阈值</label>
                                <input type="number" class="form-control" name="threshold" placeholder="如: 35">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">操作符</label>
                                <select class="form-select" name="operator">
                                    <option value=">">大于 ></option>
                                    <option value="<">小于 <</option>
                                    <option value=">=">大于等于 >=</option>
                                    <option value="<=">小于等于 <=</option>
                                    <option value="=">等于 =</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">严重程度</label>
                                <select class="form-select" name="severity" required>
                                    <option value="">选择程度</option>
                                    <option value="warning">警告</option>
                                    <option value="danger">危险</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">预警消息</label>
                            <textarea class="form-control" name="message" rows="3" placeholder="预警触发时发送的消息内容" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">目标租户</label>
                                <select class="form-select" name="targetTenants">
                                    <option value="all">所有租户</option>
                                    <option value="specific">指定租户</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="isActive" id="alertIsActive" checked>
                                    <label class="form-check-label" for="alertIsActive">启用预警</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="saveAlertConfig()">保存预警</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试天气API连接
        function testWeatherAPI(configId) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            button.disabled = true;
            
            fetch(`/saas-admin/weather/config/${configId}/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`连接测试成功！\n响应时间: ${data.data.responseTime}\n测试数据: 温度${data.data.testData.temperature}℃, 湿度${data.data.testData.humidity}%, 天气${data.data.testData.weather}`);
                } else {
                    alert('连接测试失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('测试失败: ' + error.message);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        // 保存天气API配置
        function saveWeatherConfig() {
            const form = document.getElementById('addConfigForm');
            const formData = new FormData(form);
            
            // 处理多选框
            const features = [];
            form.querySelectorAll('input[name="features"]:checked').forEach(checkbox => {
                features.push(checkbox.value);
            });
            
            const data = {
                name: formData.get('name'),
                provider: formData.get('provider'),
                apiKey: formData.get('apiKey'),
                updateInterval: formData.get('updateInterval'),
                isDefault: formData.get('isDefault') === 'on',
                isActive: formData.get('isActive') === 'on',
                features: features
            };
            
            fetch('/saas-admin/weather/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('配置保存成功！');
                    location.reload();
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('保存失败: ' + error.message);
            });
        }
        
        // 保存预警配置
        function saveAlertConfig() {
            const form = document.getElementById('addAlertForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            data.isActive = formData.get('isActive') === 'on';
            
            fetch('/saas-admin/weather/alerts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('预警配置保存成功！');
                    location.reload();
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('保存失败: ' + error.message);
            });
        }
        
        // 删除配置
        function deleteConfig(configId) {
            if (confirm('确定要删除这个天气配置吗？')) {
                fetch(`/saas-admin/weather/config/${configId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('配置删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }
        
        // 删除预警
        function deleteAlert(alertId) {
            if (confirm('确定要删除这个预警配置吗？')) {
                fetch(`/saas-admin/weather/alerts/${alertId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('预警配置删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }
        
        // 编辑配置（简化实现）
        function editConfig(configId) {
            alert('编辑功能开发中...');
        }
        
        // 编辑预警（简化实现）
        function editAlert(alertId) {
            alert('编辑功能开发中...');
        }
    </script>
</body>
</html>