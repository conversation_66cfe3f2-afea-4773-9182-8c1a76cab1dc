const express = require('express');
const router = express.Router();

// 库存预警配置管理
router.get('/', async (req, res) => {
  try {
    // 模拟库存预警配置数据
    const inventoryAlerts = [
      {
        id: 1,
        alertName: '饲料库存预警',
        category: 'feed',
        alertType: 'low_stock',
        threshold: 10,
        unit: '袋',
        isActive: true,
        severity: 'warning',
        message: '饲料库存不足，建议及时补充',
        notificationMethods: ['email', 'sms', 'system'],
        targetTenants: 'all',
        triggeredCount: 12,
        lastTriggered: '2024-01-20T10:30:00Z',
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        alertName: '药品过期预警',
        category: 'medicine',
        alertType: 'expiry',
        threshold: 7,
        unit: '天',
        isActive: true,
        severity: 'danger',
        message: '药品即将过期，请及时处理',
        notificationMethods: ['email', 'system'],
        targetTenants: 'all',
        triggeredCount: 3,
        lastTriggered: '2024-01-19T15:20:00Z',
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 3,
        alertName: '设备维护提醒',
        category: 'equipment',
        alertType: 'maintenance',
        threshold: 30,
        unit: '天',
        isActive: true,
        severity: 'info',
        message: '设备需要进行定期维护检查',
        notificationMethods: ['system'],
        targetTenants: 'specific',
        triggeredCount: 8,
        lastTriggered: '2024-01-18T09:15:00Z',
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 4,
        alertName: '库存异常变动预警',
        category: 'all',
        alertType: 'abnormal_change',
        threshold: 20,
        unit: '%',
        isActive: false,
        severity: 'warning',
        message: '库存出现异常变动，请检查库存记录',
        notificationMethods: ['email', 'system'],
        targetTenants: 'all',
        triggeredCount: 1,
        lastTriggered: '2024-01-15T14:45:00Z',
        createdAt: '2024-01-01T00:00:00Z'
      }
    ];

    // 模拟库存类别配置
    const categoryConfig = [
      { category: 'feed', name: '饲料', minStockDefault: 10, maxStockDefault: 100, isActive: true },
      { category: 'medicine', name: '药品', minStockDefault: 5, maxStockDefault: 50, isActive: true },
      { category: 'equipment', name: '设备', minStockDefault: 1, maxStockDefault: 20, isActive: true },
      { category: 'materials', name: '物料', minStockDefault: 20, maxStockDefault: 200, isActive: true }
    ];

    // 模拟预警统计数据
    const alertStats = {
      totalAlerts: inventoryAlerts.length,
      activeAlerts: inventoryAlerts.filter(a => a.isActive).length,
      triggeredToday: 5,
      totalTriggered: inventoryAlerts.reduce((sum, a) => sum + a.triggeredCount, 0),
      criticalAlerts: inventoryAlerts.filter(a => a.severity === 'danger' && a.isActive).length,
      resolvedToday: 3
    };

    if (req.accepts('html')) {
      return res.render('inventory/index', {
        title: '库存预警配置',
        inventoryAlerts,
        categoryConfig,
        alertStats
      });
    } else {
      res.json({
        success: true,
        message: '获取库存预警配置成功',
        data: {
          inventoryAlerts,
          categoryConfig,
          alertStats
        }
      });
    }
  } catch (error) {
    console.error('获取库存预警配置失败:', error);
    if (req.accepts('html')) {
      return res.render('error', {
        title: '错误',
        error: '获取库存预警配置失败',
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '获取库存预警配置失败',
        error: error.message
      });
    }
  }
});

// 创建库存预警配置
router.post('/alerts', async (req, res) => {
  try {
    const {
      alertName,
      category,
      alertType,
      threshold,
      unit,
      isActive,
      severity,
      message,
      notificationMethods,
      targetTenants
    } = req.body;

    // 验证必填字段
    if (!alertName || !category || !alertType || !threshold || !severity || !message) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 模拟保存预警配置
    const newAlert = {
      id: Math.floor(Math.random() * 1000) + 100,
      alertName,
      category,
      alertType,
      threshold: parseFloat(threshold),
      unit: unit || '个',
      isActive: Boolean(isActive),
      severity,
      message,
      notificationMethods: Array.isArray(notificationMethods) ? notificationMethods : ['system'],
      targetTenants: targetTenants || 'all',
      triggeredCount: 0,
      lastTriggered: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '库存预警配置创建成功',
      data: newAlert
    });
  } catch (error) {
    console.error('创建库存预警配置失败:', error);
    res.status(500).json({
      success: false,
      message: '创建库存预警配置失败',
      error: error.message
    });
  }
});

// 更新库存预警配置
router.put('/alerts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 模拟更新预警配置
    const updatedAlert = {
      id: parseInt(id),
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '库存预警配置更新成功',
      data: updatedAlert
    });
  } catch (error) {
    console.error('更新库存预警配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新库存预警配置失败',
      error: error.message
    });
  }
});

// 删除库存预警配置
router.delete('/alerts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟删除预警配置
    res.json({
      success: true,
      message: '库存预警配置删除成功',
      data: { id: parseInt(id) }
    });
  } catch (error) {
    console.error('删除库存预警配置失败:', error);
    res.status(500).json({
      success: false,
      message: '删除库存预警配置失败',
      error: error.message
    });
  }
});

// 创建或更新库存类别配置
router.post('/categories', async (req, res) => {
  try {
    const {
      category,
      name,
      minStockDefault,
      maxStockDefault,
      isActive
    } = req.body;

    // 验证必填字段
    if (!category || !name || !minStockDefault || !maxStockDefault) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 模拟保存类别配置
    const newCategory = {
      category,
      name,
      minStockDefault: parseInt(minStockDefault),
      maxStockDefault: parseInt(maxStockDefault),
      isActive: Boolean(isActive),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '库存类别配置保存成功',
      data: newCategory
    });
  } catch (error) {
    console.error('保存库存类别配置失败:', error);
    res.status(500).json({
      success: false,
      message: '保存库存类别配置失败',
      error: error.message
    });
  }
});

// 测试预警规则
router.post('/alerts/:id/test', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟预警测试
    const testResult = {
      success: true,
      alertId: parseInt(id),
      testTime: new Date().toISOString(),
      matchedTenants: 8,
      estimatedNotifications: 24,
      testData: {
        category: 'feed',
        currentStock: 8,
        threshold: 10,
        triggered: true
      }
    };

    res.json({
      success: true,
      message: '预警规则测试成功',
      data: testResult
    });
  } catch (error) {
    console.error('测试预警规则失败:', error);
    res.status(500).json({
      success: false,
      message: '测试预警规则失败',
      error: error.message
    });
  }
});

// 获取预警历史记录
router.get('/history', async (req, res) => {
  try {
    const { timeRange = '7d', alertId, tenantId } = req.query;
    
    // 模拟预警历史数据
    const history = [
      {
        id: 1,
        alertId: 1,
        alertName: '饲料库存预警',
        tenantId: 'T123456789',
        tenantName: '绿野生态农场',
        triggeredAt: '2024-01-20T10:30:00Z',
        severity: 'warning',
        message: '饲料库存不足，建议及时补充',
        isResolved: true,
        resolvedAt: '2024-01-20T14:15:00Z',
        resolvedBy: 'system'
      },
      {
        id: 2,
        alertId: 2,
        alertName: '药品过期预警',
        tenantId: 'T123456790',
        tenantName: '明珠养殖基地',
        triggeredAt: '2024-01-19T15:20:00Z',
        severity: 'danger',
        message: '药品即将过期，请及时处理',
        isResolved: false,
        resolvedAt: null,
        resolvedBy: null
      }
    ];

    const summary = {
      total: history.length,
      resolved: history.filter(h => h.isResolved).length,
      pending: history.filter(h => !h.isResolved).length,
      critical: history.filter(h => h.severity === 'danger').length
    };

    if (req.accepts('html')) {
      return res.render('inventory/history', {
        title: '库存预警历史',
        history,
        summary,
        timeRange
      });
    } else {
      res.json({
        success: true,
        message: '获取预警历史记录成功',
        data: { history, summary }
      });
    }
  } catch (error) {
    console.error('获取预警历史记录失败:', error);
    if (req.accepts('html')) {
      return res.render('error', {
        title: '错误',
        error: '获取预警历史记录失败',
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '获取预警历史记录失败',
        error: error.message
      });
    }
  }
});

// 批量操作预警配置
router.post('/alerts/batch', async (req, res) => {
  try {
    const { action, alertIds } = req.body;
    
    if (!action || !Array.isArray(alertIds) || alertIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '无效的批量操作参数'
      });
    }

    let message = '';
    switch (action) {
      case 'activate':
        message = `成功启用 ${alertIds.length} 个预警配置`;
        break;
      case 'deactivate':
        message = `成功停用 ${alertIds.length} 个预警配置`;
        break;
      case 'delete':
        message = `成功删除 ${alertIds.length} 个预警配置`;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: '不支持的批量操作'
        });
    }

    res.json({
      success: true,
      message,
      data: {
        action,
        processedCount: alertIds.length,
        alertIds
      }
    });
  } catch (error) {
    console.error('批量操作失败:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败',
      error: error.message
    });
  }
});

module.exports = router;