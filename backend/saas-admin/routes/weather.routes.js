const express = require('express');
const router = express.Router();

// 天气配置管理
router.get('/', async (req, res) => {
  try {
    // 模拟天气配置数据
    const weatherConfig = [
      {
        id: 1,
        name: '和风天气API',
        provider: 'qweather',
        apiKey: 'your_qweather_key',
        isDefault: true,
        isActive: true,
        updateInterval: 30,
        features: ['current', 'forecast', 'alerts'],
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '高德天气API',
        provider: 'amap',
        apiKey: 'your_amap_key',
        isDefault: false,
        isActive: false,
        updateInterval: 60,
        features: ['current', 'forecast'],
        createdAt: '2024-01-05T00:00:00Z'
      }
    ];

    // 模拟天气预警配置
    const alertConfig = [
      {
        id: 1,
        alertType: 'temperature',
        alertName: '高温预警',
        threshold: 35,
        operator: '>',
        isActive: true,
        severity: 'warning',
        message: '当前温度过高，注意鹅群防暑降温',
        targetTenants: 'all'
      },
      {
        id: 2,
        alertType: 'temperature',
        alertName: '低温预警',
        threshold: -5,
        operator: '<',
        isActive: true,
        severity: 'danger',
        message: '当前温度过低，注意鹅群保暖',
        targetTenants: 'all'
      },
      {
        id: 3,
        alertType: 'humidity',
        alertName: '高湿度预警',
        threshold: 85,
        operator: '>',
        isActive: true,
        severity: 'warning',
        message: '湿度过高，注意通风防病',
        targetTenants: 'all'
      },
      {
        id: 4,
        alertType: 'weather',
        alertName: '恶劣天气预警',
        condition: ['storm', 'hail', 'tornado'],
        isActive: true,
        severity: 'danger',
        message: '恶劣天气来临，请做好防护措施',
        targetTenants: 'all'
      }
    ];

    // 模拟统计数据
    const stats = {
      totalApiCalls: 15420,
      todayApiCalls: 312,
      activeAlerts: 2,
      coveredTenants: 28,
      averageResponseTime: '120ms',
      successRate: '99.8%'
    };

    if (req.accepts('html')) {
      return res.render('weather/index', {
        title: '天气配置管理',
        weatherConfig,
        alertConfig,
        stats
      });
    } else {
      res.json({
        success: true,
        message: '获取天气配置成功',
        data: {
          weatherConfig,
          alertConfig,
          stats
        }
      });
    }
  } catch (error) {
    console.error('获取天气配置失败:', error);
    if (req.accepts('html')) {
      return res.render('error', {
        title: '错误',
        error: '获取天气配置失败',
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '获取天气配置失败',
        error: error.message
      });
    }
  }
});

// 创建或更新天气API配置
router.post('/config', async (req, res) => {
  try {
    const {
      name,
      provider,
      apiKey,
      isDefault,
      isActive,
      updateInterval,
      features
    } = req.body;

    // 验证必填字段
    if (!name || !provider || !apiKey) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段: name, provider, apiKey'
      });
    }

    // 模拟保存配置
    const newConfig = {
      id: Math.floor(Math.random() * 1000) + 100,
      name,
      provider,
      apiKey,
      isDefault: Boolean(isDefault),
      isActive: Boolean(isActive),
      updateInterval: parseInt(updateInterval) || 30,
      features: Array.isArray(features) ? features : ['current', 'forecast'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '天气API配置保存成功',
      data: newConfig
    });
  } catch (error) {
    console.error('保存天气配置失败:', error);
    res.status(500).json({
      success: false,
      message: '保存天气配置失败',
      error: error.message
    });
  }
});

// 创建或更新天气预警配置
router.post('/alerts', async (req, res) => {
  try {
    const {
      alertType,
      alertName,
      threshold,
      operator,
      condition,
      isActive,
      severity,
      message,
      targetTenants
    } = req.body;

    // 验证必填字段
    if (!alertType || !alertName || !severity || !message) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 模拟保存预警配置
    const newAlert = {
      id: Math.floor(Math.random() * 1000) + 100,
      alertType,
      alertName,
      threshold: threshold ? parseFloat(threshold) : null,
      operator: operator || '=',
      condition: Array.isArray(condition) ? condition : [],
      isActive: Boolean(isActive),
      severity,
      message,
      targetTenants: targetTenants || 'all',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '天气预警配置保存成功',
      data: newAlert
    });
  } catch (error) {
    console.error('保存预警配置失败:', error);
    res.status(500).json({
      success: false,
      message: '保存预警配置失败',
      error: error.message
    });
  }
});

// 测试天气API连接
router.post('/config/:id/test', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟API测试
    const testResult = {
      success: true,
      responseTime: Math.floor(Math.random() * 200) + 50 + 'ms',
      status: 'connected',
      lastUpdate: new Date().toISOString(),
      testData: {
        temperature: 22,
        humidity: 65,
        weather: '晴'
      }
    };

    res.json({
      success: true,
      message: '天气API测试成功',
      data: testResult
    });
  } catch (error) {
    console.error('测试天气API失败:', error);
    res.status(500).json({
      success: false,
      message: '测试天气API失败',
      error: error.message
    });
  }
});

// 删除天气配置
router.delete('/config/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟删除配置
    res.json({
      success: true,
      message: '天气配置删除成功',
      data: { id: parseInt(id) }
    });
  } catch (error) {
    console.error('删除天气配置失败:', error);
    res.status(500).json({
      success: false,
      message: '删除天气配置失败',
      error: error.message
    });
  }
});

// 删除预警配置
router.delete('/alerts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟删除预警配置
    res.json({
      success: true,
      message: '预警配置删除成功',
      data: { id: parseInt(id) }
    });
  } catch (error) {
    console.error('删除预警配置失败:', error);
    res.status(500).json({
      success: false,
      message: '删除预警配置失败',
      error: error.message
    });
  }
});

// 获取天气统计报告
router.get('/report', async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;
    
    // 模拟统计报告数据
    const report = {
      timeRange,
      summary: {
        totalRequests: 15420,
        successRate: 99.8,
        averageResponseTime: 120,
        activeAlerts: 2,
        alertsSent: 45
      },
      usage: [
        { date: '2024-01-20', requests: 312, alerts: 2 },
        { date: '2024-01-19', requests: 298, alerts: 1 },
        { date: '2024-01-18', requests: 285, alerts: 3 }
      ],
      topTenants: [
        { tenantName: '绿野生态农场', requests: 1250, alerts: 8 },
        { tenantName: '明珠养殖基地', requests: 1100, alerts: 5 },
        { tenantName: '天源农牧', requests: 980, alerts: 3 }
      ]
    };

    if (req.accepts('html')) {
      return res.render('weather/report', {
        title: '天气服务报告',
        report
      });
    } else {
      res.json({
        success: true,
        message: '获取天气统计报告成功',
        data: report
      });
    }
  } catch (error) {
    console.error('获取天气报告失败:', error);
    if (req.accepts('html')) {
      return res.render('error', {
        title: '错误',
        error: '获取天气报告失败',
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '获取天气报告失败',
        error: error.message
      });
    }
  }
});

module.exports = router;