const express = require('express');
const router = express.Router();

// 任务模板管理
router.get('/', async (req, res) => {
  try {
    // 模拟任务模板数据
    const taskTemplates = [
      {
        id: 1,
        templateName: '疫苗接种提醒',
        category: 'health',
        description: '定期疫苗接种任务模板',
        priority: 'high',
        estimatedDuration: 60,
        instructions: '按照疫苗接种计划进行操作',
        requiredFields: ['vaccine_type', 'batch_number', 'dosage'],
        isActive: true,
        usageCount: 156,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        templateName: '饲料补充检查',
        category: 'feeding',
        description: '定期检查饲料库存并补充',
        priority: 'medium',
        estimatedDuration: 30,
        instructions: '检查饲料存量，低于安全库存时及时补充',
        requiredFields: ['feed_type', 'current_stock', 'required_amount'],
        isActive: true,
        usageCount: 243,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 3,
        templateName: '环境清洁消毒',
        category: 'environment',
        description: '鹅舍环境清洁和消毒工作',
        priority: 'high',
        estimatedDuration: 120,
        instructions: '按照清洁消毒标准操作程序执行',
        requiredFields: ['area_location', 'disinfectant_type', 'completion_photos'],
        isActive: true,
        usageCount: 89,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 4,
        templateName: '生产数据记录',
        category: 'production',
        description: '每日产蛋量和生产指标记录',
        priority: 'medium',
        estimatedDuration: 15,
        instructions: '准确记录产蛋量、死亡数、异常情况等',
        requiredFields: ['egg_count', 'mortality_count', 'abnormal_notes'],
        isActive: true,
        usageCount: 312,
        createdAt: '2024-01-01T00:00:00Z'
      }
    ];

    // 模拟任务分配规则
    const assignmentRules = [
      {
        id: 1,
        ruleName: '疫苗接种自动分配',
        triggerType: 'schedule',
        triggerCondition: 'monthly',
        templateId: 1,
        assigneeType: 'role',
        assigneeValue: 'veterinarian',
        priority: 'high',
        isActive: true,
        targetTenants: 'all'
      },
      {
        id: 2,
        ruleName: '饲料库存预警任务',
        triggerType: 'inventory_alert',
        triggerCondition: 'low_stock',
        templateId: 2,
        assigneeType: 'department',
        assigneeValue: 'feeding_department',
        priority: 'medium',
        isActive: true,
        targetTenants: 'all'
      }
    ];

    // 模拟统计数据
    const taskStats = {
      totalTemplates: taskTemplates.length,
      activeTemplates: taskTemplates.filter(t => t.isActive).length,
      totalUsage: taskTemplates.reduce((sum, t) => sum + t.usageCount, 0),
      activeRules: assignmentRules.filter(r => r.isActive).length,
      todayAssigned: 45,
      completedToday: 32
    };

    if (req.accepts('html')) {
      return res.render('tasks/index', {
        title: '任务管理中心',
        taskTemplates,
        assignmentRules,
        taskStats
      });
    } else {
      res.json({
        success: true,
        message: '获取任务配置成功',
        data: {
          taskTemplates,
          assignmentRules,
          taskStats
        }
      });
    }
  } catch (error) {
    console.error('获取任务配置失败:', error);
    if (req.accepts('html')) {
      return res.render('error', {
        title: '错误',
        error: '获取任务配置失败',
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '获取任务配置失败',
        error: error.message
      });
    }
  }
});

// 创建任务模板
router.post('/templates', async (req, res) => {
  try {
    const {
      templateName,
      category,
      description,
      priority,
      estimatedDuration,
      instructions,
      requiredFields,
      isActive
    } = req.body;

    // 验证必填字段
    if (!templateName || !category || !description || !instructions) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 模拟保存任务模板
    const newTemplate = {
      id: Math.floor(Math.random() * 1000) + 100,
      templateName,
      category,
      description,
      priority: priority || 'medium',
      estimatedDuration: parseInt(estimatedDuration) || 60,
      instructions,
      requiredFields: Array.isArray(requiredFields) ? requiredFields : [],
      isActive: Boolean(isActive),
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '任务模板创建成功',
      data: newTemplate
    });
  } catch (error) {
    console.error('创建任务模板失败:', error);
    res.status(500).json({
      success: false,
      message: '创建任务模板失败',
      error: error.message
    });
  }
});

// 更新任务模板
router.put('/templates/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 模拟更新任务模板
    const updatedTemplate = {
      id: parseInt(id),
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '任务模板更新成功',
      data: updatedTemplate
    });
  } catch (error) {
    console.error('更新任务模板失败:', error);
    res.status(500).json({
      success: false,
      message: '更新任务模板失败',
      error: error.message
    });
  }
});

// 删除任务模板
router.delete('/templates/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟删除任务模板
    res.json({
      success: true,
      message: '任务模板删除成功',
      data: { id: parseInt(id) }
    });
  } catch (error) {
    console.error('删除任务模板失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任务模板失败',
      error: error.message
    });
  }
});

// 创建任务分配规则
router.post('/assignment-rules', async (req, res) => {
  try {
    const {
      ruleName,
      triggerType,
      triggerCondition,
      templateId,
      assigneeType,
      assigneeValue,
      priority,
      isActive,
      targetTenants
    } = req.body;

    // 验证必填字段
    if (!ruleName || !triggerType || !templateId || !assigneeType || !assigneeValue) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 模拟保存分配规则
    const newRule = {
      id: Math.floor(Math.random() * 1000) + 100,
      ruleName,
      triggerType,
      triggerCondition,
      templateId: parseInt(templateId),
      assigneeType,
      assigneeValue,
      priority: priority || 'medium',
      isActive: Boolean(isActive),
      targetTenants: targetTenants || 'all',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '任务分配规则创建成功',
      data: newRule
    });
  } catch (error) {
    console.error('创建任务分配规则失败:', error);
    res.status(500).json({
      success: false,
      message: '创建任务分配规则失败',
      error: error.message
    });
  }
});

// 获取任务执行报告
router.get('/reports', async (req, res) => {
  try {
    const { timeRange = '7d', tenantId, category } = req.query;
    
    // 模拟任务报告数据
    const report = {
      timeRange,
      summary: {
        totalTasks: 1248,
        completedTasks: 1056,
        pendingTasks: 125,
        overdueTasks: 67,
        completionRate: 84.6,
        averageCompletionTime: '2.3小时'
      },
      categoryStats: [
        { category: 'health', name: '健康管理', count: 456, completed: 398, completionRate: 87.3 },
        { category: 'feeding', name: '饲养管理', count: 334, completed: 289, completionRate: 86.5 },
        { category: 'environment', name: '环境管理', count: 234, completed: 201, completionRate: 85.9 },
        { category: 'production', name: '生产记录', count: 224, completed: 168, completionRate: 75.0 }
      ],
      tenantStats: [
        { tenantName: '绿野生态农场', assigned: 89, completed: 78, completionRate: 87.6 },
        { tenantName: '明珠养殖基地', assigned: 76, completed: 65, completionRate: 85.5 },
        { tenantName: '天源农牧', assigned: 67, completed: 52, completionRate: 77.6 }
      ],
      trends: [
        { date: '2024-01-20', assigned: 45, completed: 39 },
        { date: '2024-01-19', assigned: 52, completed: 48 },
        { date: '2024-01-18', assigned: 48, completed: 41 }
      ]
    };

    if (req.accepts('html')) {
      return res.render('tasks/reports', {
        title: '任务执行报告',
        report,
        timeRange
      });
    } else {
      res.json({
        success: true,
        message: '获取任务报告成功',
        data: report
      });
    }
  } catch (error) {
    console.error('获取任务报告失败:', error);
    if (req.accepts('html')) {
      return res.render('error', {
        title: '错误',
        error: '获取任务报告失败',
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '获取任务报告失败',
        error: error.message
      });
    }
  }
});

// 手动分配任务
router.post('/assign', async (req, res) => {
  try {
    const {
      templateId,
      tenantIds,
      assigneeType,
      assigneeValue,
      deadline,
      priority,
      customInstructions
    } = req.body;

    // 验证必填字段
    if (!templateId || !tenantIds || !Array.isArray(tenantIds) || tenantIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段或租户列表'
      });
    }

    // 模拟任务分配
    const assignedTasks = tenantIds.map(tenantId => ({
      id: Math.floor(Math.random() * 10000) + 1000,
      templateId: parseInt(templateId),
      tenantId,
      assigneeType: assigneeType || 'auto',
      assigneeValue: assigneeValue || 'system',
      priority: priority || 'medium',
      deadline,
      customInstructions,
      status: 'pending',
      assignedAt: new Date().toISOString()
    }));

    res.json({
      success: true,
      message: `成功分配任务给 ${tenantIds.length} 个租户`,
      data: {
        assignedCount: assignedTasks.length,
        tasks: assignedTasks
      }
    });
  } catch (error) {
    console.error('任务分配失败:', error);
    res.status(500).json({
      success: false,
      message: '任务分配失败',
      error: error.message
    });
  }
});

// 获取任务类别配置
router.get('/categories', async (req, res) => {
  try {
    // 模拟任务类别配置
    const categories = [
      {
        code: 'health',
        name: '健康管理',
        description: '疫苗接种、健康检查、疾病预防等',
        icon: 'bi-heart-pulse',
        color: '#dc3545',
        defaultPriority: 'high',
        isActive: true
      },
      {
        code: 'feeding',
        name: '饲养管理',
        description: '饲料配送、营养管理、饮水检查等',
        icon: 'bi-bowl',
        color: '#28a745',
        defaultPriority: 'medium',
        isActive: true
      },
      {
        code: 'environment',
        name: '环境管理',
        description: '清洁消毒、温湿度控制、通风管理等',
        icon: 'bi-house',
        color: '#17a2b8',
        defaultPriority: 'medium',
        isActive: true
      },
      {
        code: 'production',
        name: '生产记录',
        description: '产蛋记录、生产数据统计、质量检查等',
        icon: 'bi-clipboard-data',
        color: '#fd7e14',
        defaultPriority: 'low',
        isActive: true
      },
      {
        code: 'maintenance',
        name: '设备维护',
        description: '设备检修、设施维护、工具保养等',
        icon: 'bi-gear',
        color: '#6f42c1',
        defaultPriority: 'medium',
        isActive: true
      }
    ];

    res.json({
      success: true,
      message: '获取任务类别成功',
      data: categories
    });
  } catch (error) {
    console.error('获取任务类别失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务类别失败',
      error: error.message
    });
  }
});

// 批量操作任务模板
router.post('/templates/batch', async (req, res) => {
  try {
    const { action, templateIds } = req.body;
    
    if (!action || !Array.isArray(templateIds) || templateIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '无效的批量操作参数'
      });
    }

    let message = '';
    switch (action) {
      case 'activate':
        message = `成功启用 ${templateIds.length} 个任务模板`;
        break;
      case 'deactivate':
        message = `成功停用 ${templateIds.length} 个任务模板`;
        break;
      case 'delete':
        message = `成功删除 ${templateIds.length} 个任务模板`;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: '不支持的批量操作'
        });
    }

    res.json({
      success: true,
      message,
      data: {
        action,
        processedCount: templateIds.length,
        templateIds
      }
    });
  } catch (error) {
    console.error('批量操作失败:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败',
      error: error.message
    });
  }
});

module.exports = router;