<!-- pages/shop/goods-detail.wxml -->
<view class="detail-container">
  <!-- 商品图片轮播 -->
  <view class="image-gallery">
    <swiper class="goods-swiper" indicator-dots="{{true}}" autoplay="{{false}}" interval="{{5000}}" duration="{{500}}">
      <swiper-item wx:for="{{goods.images || [goods.image]}}" wx:key="*this">
        <image class="goods-image" src="{{item}}" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    <view class="image-indicator">{{imageIndex + 1}}/{{goods.images ? goods.images.length : 1}}</view>
  </view>

  <!-- 商品信息区域 -->
  <view class="product-info">
    <!-- 价格区域 -->
    <view class="price-section">
      <view class="current-price">¥{{currentPrice || goods.price || '99.99'}}</view>
      <view class="original-price" wx:if="{{goods.originalPrice}}">¥{{goods.originalPrice}}</view>
    </view>

    <!-- 商品名称 -->
    <view class="product-title">{{goods.name || '优质鹅饲料'}}</view>

    <!-- 商品描述 -->
    <view class="product-description">
      <text>{{goods.description || '专为鹅类设计的高营养饲料，促进健康成长，富含蛋白质和维生素，适合各年龄段的鹅类食用。'}}</text>
    </view>

    <!-- 分割线 -->
    <view class="divider"></view>

    <!-- 商品信息 -->
    <view class="product-stats">
      <view class="stat-item">
        <text class="stat-label">库存:</text>
        <text class="stat-value">{{goods.stock || 100}}件</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">销量:</text>
        <text class="stat-value">{{goods.sales || 1200}}件</text>
      </view>
    </view>

    <!-- 分割线 -->
    <view class="divider"></view>

    <!-- SKU选择区域 -->
    <view class="sku-section">
      <view class="sku-title">选择规格</view>
      <view class="sku-options">
        <view class="sku-group" wx:for="{{goods.skuList}}" wx:key="name" wx:for-item="skuGroup">
          <view class="sku-label">{{skuGroup.name}}</view>
          <view class="sku-values">
            <view 
              class="sku-value {{selectedSku[skuGroup.name] === item.value ? 'active' : ''}}" 
              wx:for="{{skuGroup.values}}" 
              wx:key="value"
              bindtap="onSkuSelect"
              data-group="{{skuGroup.name}}"
              data-value="{{item.value}}"
            >
              {{item.value}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分割线 -->
    <view class="divider"></view>
  </view>

  <!-- 商品详情 -->
  <view class="detail-section">
    <view class="section-title">商品详情</view>
    <view class="detail-content">
      <view class="detail-images" wx:if="{{goods.detailImages}}">
        <image 
          class="detail-image" 
          wx:for="{{goods.detailImages}}" 
          wx:key="*this"
          src="{{item}}" 
          mode="widthFix"
        ></image>
      </view>
      <view class="detail-text">
        <rich-text nodes="{{goods.detailContent || '暂无详细信息'}}"></rich-text>
      </view>
    </view>
  </view>

  <!-- 商品参数 -->
  <view class="params-section">
    <view class="section-title">商品参数</view>
    <view class="params-list">
      <view class="param-item" wx:for="{{goods.params}}" wx:key="name">
        <text class="param-name">{{item.name}}</text>
        <text class="param-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 商品评价 -->
  <view class="reviews-section">
    <view class="section-header">
      <view class="section-title">商品评价</view>
      <view class="review-summary">
        <view class="rating-score">{{goods.rating || 4.8}}</view>
        <view class="rating-stars">
          <text class="star {{index < Math.floor(goods.rating || 4.8) ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
        </view>
        <view class="review-count">({{goods.reviewCount || 156}}条评价)</view>
      </view>
    </view>
    
    <view class="reviews-list">
      <view class="review-item" wx:for="{{goods.reviews}}" wx:key="id">
        <view class="review-header">
          <image class="reviewer-avatar" src="{{item.avatar || '/images/default_avatar.png'}}"></image>
          <view class="reviewer-info">
            <view class="reviewer-name">{{item.username}}</view>
            <view class="review-date">{{item.date}}</view>
          </view>
          <view class="review-rating">
            <text class="star {{index < item.rating ? 'filled' : ''}}" wx:for="{{5}}" wx:key="*this">★</text>
          </view>
        </view>
        <view class="review-content">{{item.content}}</view>
        <view class="review-images" wx:if="{{item.images}}">
          <image 
            class="review-image" 
            wx:for="{{item.images}}" 
            wx:for-item="imgUrl"
            wx:key="*this"
            src="{{imgUrl}}" 
            mode="aspectFill"
          ></image>
        </view>
      </view>
    </view>
    
    <view class="view-all-reviews" bindtap="onViewAllReviews">
      <text>查看全部评价</text>
      <text class="arrow">></text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-button cart-button" bindtap="onAddToCart">
      <text class="button-icon">🛒</text>
      <text class="button-text">加入购物车</text>
    </view>
    <view class="action-button buy-button" bindtap="onBuyNow">
      <text class="button-icon">⚡</text>
      <text class="button-text">立即购买</text>
    </view>
  </view>
</view>