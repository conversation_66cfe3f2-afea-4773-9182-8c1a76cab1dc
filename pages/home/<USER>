/* pages/home/<USER>/

/* 首页容器 */
.container {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 0;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 用户信息横幅 */
.user-info-banner {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  color: var(--text-inverse);
  margin: var(--space-2xl) var(--space-2xl) 0 var(--space-2xl);
  position: relative;
  overflow: hidden;
}

.user-info-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: rgba(255, 255, 255, 0.2);
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 8rpx;
  display: block;
}

.farm-name {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
  display: block;
}

.weather-info {
  flex-shrink: 0;
  margin-left: 24rpx;
}

/* 公告栏 */
.announcement-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 2rpx solid #13c2c2;
}

.announcement-list {
  /* 公告列表容器 */
}

.announcement-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-content {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
}

.announcement-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.announcement-time {
  flex-shrink: 0;
}

.announcement-time text {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
}

/* 任务部分 */
.task-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 2rpx solid #722ed1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.more-text {
  font-size: 28rpx;
  color: #666666;
}

/* 任务列表 */
.task-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  border-left: 6rpx solid #e0e0e0;
  border-top: 2rpx solid transparent;
  border-right: 2rpx solid transparent;
  border-radius: 16rpx;
  margin: 8rpx 0;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.task-item:last-child {
  border-bottom: 1rpx solid #f0f0f0;
}

/* 根据日龄阶段设置不同的框线颜色 - 与任务列表页保持一致 */
/* 开口药阶段（1-5天）- 紫色系 */
.task-item[data-day-age="1"],
.task-item[data-day-age="2"],
.task-item[data-day-age="3"],
.task-item[data-day-age="4"],
.task-item[data-day-age="5"] {
  border-left-color: #722ed1;
  border-top-color: rgba(114, 46, 209, 0.3);
  border-right-color: rgba(114, 46, 209, 0.3);
  border-bottom-color: rgba(114, 46, 209, 0.3);
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.05) 0%, #ffffff 100%);
}

/* 疫苗接种阶段（6天）- 深红色系 */
.task-item[data-day-age="6"] {
  border-left-color: #c41e3a;
  border-top-color: rgba(196, 30, 58, 0.3);
  border-right-color: rgba(196, 30, 58, 0.3);
  border-bottom-color: rgba(196, 30, 58, 0.3);
  background: linear-gradient(135deg, rgba(196, 30, 58, 0.05) 0%, #ffffff 100%);
}

/* 保肝护肾阶段（7-8天）- 橙色系 */
.task-item[data-day-age="7"],
.task-item[data-day-age="8"] {
  border-left-color: #fa8c16;
  border-top-color: rgba(250, 140, 22, 0.3);
  border-right-color: rgba(250, 140, 22, 0.3);
  border-bottom-color: rgba(250, 140, 22, 0.3);
  background: linear-gradient(135deg, rgba(250, 140, 22, 0.05) 0%, #ffffff 100%);
}

/* 呼肠孤防治阶段（9-10天）- 青色系 */
.task-item[data-day-age="9"],
.task-item[data-day-age="10"] {
  border-left-color: #13c2c2;
  border-top-color: rgba(19, 194, 194, 0.3);
  border-right-color: rgba(19, 194, 194, 0.3);
  border-bottom-color: rgba(19, 194, 194, 0.3);
  background: linear-gradient(135deg, rgba(19, 194, 194, 0.05) 0%, #ffffff 100%);
}

/* 控料阶段（11,14,15天）- 棕色系 */
.task-item[data-day-age="11"],
.task-item[data-day-age="14"],
.task-item[data-day-age="15"] {
  border-left-color: #8b4513;
  border-top-color: rgba(139, 69, 19, 0.3);
  border-right-color: rgba(139, 69, 19, 0.3);
  border-bottom-color: rgba(139, 69, 19, 0.3);
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, #ffffff 100%);
}

/* 浆膜炎防治阶段（12-13天）- 深蓝色系 */
.task-item[data-day-age="12"],
.task-item[data-day-age="13"] {
  border-left-color: #1890ff;
  border-top-color: rgba(24, 144, 255, 0.3);
  border-right-color: rgba(24, 144, 255, 0.3);
  border-bottom-color: rgba(24, 144, 255, 0.3);
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, #ffffff 100%);
}

/* 病毒性感冒防治阶段（16-17天）- 绿色系 */
.task-item[data-day-age="16"],
.task-item[data-day-age="17"] {
  border-left-color: #52c41a;
  border-top-color: rgba(82, 196, 26, 0.3);
  border-right-color: rgba(82, 196, 26, 0.3);
  border-bottom-color: rgba(82, 196, 26, 0.3);
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, #ffffff 100%);
}

/* 痛风预防阶段（18天）- 玫红色系 */
.task-item[data-day-age="18"] {
  border-left-color: #eb2f96;
  border-top-color: rgba(235, 47, 150, 0.3);
  border-right-color: rgba(235, 47, 150, 0.3);
  border-bottom-color: rgba(235, 47, 150, 0.3);
  background: linear-gradient(135deg, rgba(235, 47, 150, 0.05) 0%, #ffffff 100%);
}

/* 日常观察阶段（19,24,25,28天）- 灰色系 */
.task-item[data-day-age="19"],
.task-item[data-day-age="24"],
.task-item[data-day-age="25"],
.task-item[data-day-age="28"] {
  border-left-color: #8c8c8c;
  border-top-color: rgba(140, 140, 140, 0.3);
  border-right-color: rgba(140, 140, 140, 0.3);
  border-bottom-color: rgba(140, 140, 140, 0.3);
  background: linear-gradient(135deg, rgba(140, 140, 140, 0.05) 0%, #ffffff 100%);
}

/* 第三针疫苗阶段（20天）- 深紫色系 */
.task-item[data-day-age="20"] {
  border-left-color: #531dab;
  border-top-color: rgba(83, 29, 171, 0.3);
  border-right-color: rgba(83, 29, 171, 0.3);
  border-bottom-color: rgba(83, 29, 171, 0.3);
  background: linear-gradient(135deg, rgba(83, 29, 171, 0.05) 0%, #ffffff 100%);
}

/* 感冒预防阶段（21天）- 粉色系 */
.task-item[data-day-age="21"] {
  border-left-color: #f759ab;
  border-top-color: rgba(247, 89, 171, 0.3);
  border-right-color: rgba(247, 89, 171, 0.3);
  border-bottom-color: rgba(247, 89, 171, 0.3);
  background: linear-gradient(135deg, rgba(247, 89, 171, 0.05) 0%, #ffffff 100%);
}

/* 呼吸道疾病预防阶段（22-23天）- 天蓝色系 */
.task-item[data-day-age="22"],
.task-item[data-day-age="23"] {
  border-left-color: #40a9ff;
  border-top-color: rgba(64, 169, 255, 0.3);
  border-right-color: rgba(64, 169, 255, 0.3);
  border-bottom-color: rgba(64, 169, 255, 0.3);
  background: linear-gradient(135deg, rgba(64, 169, 255, 0.05) 0%, #ffffff 100%);
}

/* 肠炎+抗病毒阶段（26-27天）- 黄绿色系 */
.task-item[data-day-age="26"],
.task-item[data-day-age="27"] {
  border-left-color: #a0d911;
  border-top-color: rgba(160, 217, 17, 0.3);
  border-right-color: rgba(160, 217, 17, 0.3);
  border-bottom-color: rgba(160, 217, 17, 0.3);
  background: linear-gradient(135deg, rgba(160, 217, 17, 0.05) 0%, #ffffff 100%);
}

/* 最终抗病毒阶段（29-30天）- 金色系 */
.task-item[data-day-age="29"],
.task-item[data-day-age="30"] {
  border-left-color: #fadb14;
  border-top-color: rgba(250, 219, 20, 0.3);
  border-right-color: rgba(250, 219, 20, 0.3);
  border-bottom-color: rgba(250, 219, 20, 0.3);
  background: linear-gradient(135deg, rgba(250, 219, 20, 0.05) 0%, #ffffff 100%);
}

/* 已完成任务样式 */
.task-item.completed {
  opacity: 0.6;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  margin: 8rpx 0;
}

.task-content {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: block;
}

/* 已完成任务标题划掉效果 */
.task-item.completed .task-title {
  text-decoration: line-through;
  color: #999999;
  font-weight: normal;
}

.task-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  display: block;
}

/* 已完成任务描述样式 */
.task-item.completed .task-desc {
  text-decoration: line-through;
  color: #cccccc;
}

/* 完成时间显示 */
.completed-time {
  font-size: 24rpx;
  color: #28a745;
  margin-top: 8rpx;
  display: block;
  font-weight: normal;
}

.task-time {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.task-time text {
  font-size: 24rpx;
  color: #999999;
  font-weight: normal;
}

/* 任务操作按钮 */
.task-action {
  margin-top: 8rpx;
  flex-shrink: 0;
}

.complete-btn {
  background: #f5f5f5;
  color: #333333;
  border: 1px solid #d9d9d9;
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  display: inline-block;
  text-align: center;
  min-width: 60rpx;
  transition: all 0.3s ease;
}

.complete-btn:active {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* 完成标记图标 */
.completed-icon {
  font-size: 32rpx;
  color: #28a745;
  font-weight: bold;
  margin-top: 8rpx;
  background: #e8f5e8;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
}

/* 知识库部分 */
.knowledge-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  border: 2rpx solid #eb2f96;
}

.knowledge-list {
  /* 知识库列表容器 */
}

.knowledge-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.knowledge-item:last-child {
  border-bottom: none;
}

.knowledge-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  display: block;
}

.knowledge-summary {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 12rpx;
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledge-time {
  font-size: 24rpx;
  color: #999999;
  display: block;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  color: var(--text-secondary);
  font-size: var(--font-sm);
  margin-bottom: var(--space-lg);
  line-height: 1.6;
}

.add-flock-btn {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-sm);
  font-weight: 500;
  margin-top: var(--space-md);
  min-width: 200rpx;
}

.add-flock-btn:hover {
  background: var(--primary-dark);
}

.add-flock-btn::after {
  border: none;
}

