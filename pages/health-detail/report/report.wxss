/* pages/health/report/report.wxss */
.report-container {
  min-height: 100vh;
  background-color: #f5f6f8;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 报告头部 */
.report-header {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 报告类型筛选 */
.type-scroll {
  white-space: nowrap;
  flex: 1;
  margin-right: 20rpx;
}

.type-list {
  display: inline-block;
}

.type-item {
  display: inline-block;
  padding: 16rpx 24rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.type-item.active {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: #ffffff;
  font-weight: bold;
  border: 2rpx solid rgba(0, 102, 204, 0.3);
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.3);
}

/* 导出按钮 */
.export-btn {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
  min-width: 120rpx;
}

.export-btn::after {
  border: none;
}

/* 日期选择 */
.date-selector {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.date-item {
  flex: 1;
  text-align: center;
}

.date-label {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.date-value {
  font-size: 28rpx;
  color: #333333;
  padding: 10rpx 0;
}

.date-separator {
  font-size: 28rpx;
  color: #999999;
  margin: 0 20rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 0;
  font-size: 28rpx;
  color: #999999;
}

/* 报告内容 */
.report-content {
  padding-bottom: 120rpx;
}

.report-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #0066cc;
  border-radius: 4rpx;
}

/* 健康概览 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
}

.overview-item.full {
  grid-column: span 3;
}

.item-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #0066cc;
  margin-bottom: 10rpx;
}

.item-label {
  font-size: 24rpx;
  color: #666666;
}

/* 统计列表 */
.stats-list {
  padding: 10rpx 0;
}

.stats-item {
  margin-bottom: 30rpx;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.stats-name {
  font-size: 28rpx;
  color: #333333;
}

.stats-count {
  font-size: 28rpx;
  color: #0066cc;
  font-weight: bold;
}

.stats-progress {
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  margin-right: 20rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #0066cc;
  border-radius: 6rpx;
}

.progress-rate {
  font-size: 24rpx;
  color: #666666;
  min-width: 80rpx;
  text-align: right;
}

/* 治疗效果 */
.treatment-stats {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.treatment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
}

.treatment-name {
  font-size: 28rpx;
  color: #333333;
}

.treatment-value {
  font-size: 28rpx;
  color: #0066cc;
  font-weight: bold;
}

.treatment-rate {
  font-size: 28rpx;
  color: #666666;
}

/* 趋势图表 */
.trend-chart {
  padding: 20rpx 0;
}

.chart-placeholder {
  text-align: center;
  padding: 50rpx 0;
}

.chart-placeholder image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.chart-placeholder text {
  font-size: 28rpx;
  color: #999999;
}

