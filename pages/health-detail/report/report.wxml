<!-- pages/health/report/report.wxml -->
<view class="report-container">
  <!-- 报告类型筛选和导出 -->
  <view class="report-header">
    <scroll-view class="type-scroll" scroll-x>
      <view class="type-list">
        <block wx:for="{{reportTypes}}" wx:key="id">
          <view class="type-item {{activeReportType === item.id ? 'active' : ''}}" data-type="{{item.id}}" bindtap="onReportTypeChange">
            {{item.name}}
          </view>
        </block>
      </view>
    </scroll-view>
    <button class="export-btn" bindtap="onExportReport">
      <text>导出报告</text>
    </button>
  </view>
  
  <!-- 日期选择 -->
  <view class="date-selector">
    <view class="date-item">
      <text class="date-label">开始日期</text>
      <picker mode="date" value="{{startDate}}" bindchange="onDateChange" data-type="start">
        <view class="date-value">{{startDate}}</view>
      </picker>
    </view>
    <view class="date-separator">至</view>
    <view class="date-item">
      <text class="date-label">结束日期</text>
      <picker mode="date" value="{{endDate}}" bindchange="onDateChange" data-type="end">
        <view class="date-value">{{endDate}}</view>
      </picker>
    </view>
  </view>
  
  <!-- 报告内容 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>
  
  <view wx:else class="report-content">
    <!-- 健康概览 -->
    <view class="report-section">
      <view class="section-header">
        <text class="section-title">健康概览</text>
      </view>
      <view class="overview-grid">
        <view class="overview-item">
          <text class="item-value">{{reportData.overview.totalGeese}}</text>
          <text class="item-label">总存栏</text>
        </view>
        <view class="overview-item">
          <text class="item-value">{{reportData.overview.healthyCount}}</text>
          <text class="item-label">健康数</text>
        </view>
        <view class="overview-item">
          <text class="item-value">{{reportData.overview.sickCount}}</text>
          <text class="item-label">生病数</text>
        </view>
        <view class="overview-item">
          <text class="item-value">{{reportData.overview.deathCount}}</text>
          <text class="item-label">死亡数</text>
        </view>
        <view class="overview-item full">
          <text class="item-value">{{reportData.overview.healthyRate}}</text>
          <text class="item-label">健康率</text>
        </view>
      </view>
    </view>
    
    <!-- 疾病统计 -->
    <view class="report-section">
      <view class="section-header">
        <text class="section-title">疾病统计</text>
      </view>
      <view class="stats-list">
        <block wx:for="{{reportData.diseaseStats}}" wx:key="name">
          <view class="stats-item">
            <view class="stats-info">
              <text class="stats-name">{{item.name}}</text>
              <text class="stats-count">{{item.value}}例</text>
            </view>
            <view class="stats-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{item.rate}};"></view>
              </view>
              <text class="progress-rate">{{item.rate}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 治疗效果 -->
    <view class="report-section">
      <view class="section-header">
        <text class="section-title">治疗效果</text>
      </view>
      <view class="treatment-stats">
        <block wx:for="{{reportData.treatmentStats}}" wx:key="name">
          <view class="treatment-item">
            <text class="treatment-name">{{item.name}}</text>
            <text class="treatment-value">{{item.value}}例</text>
            <text class="treatment-rate">{{item.rate}}</text>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 健康趋势 -->
    <view class="report-section">
      <c-health-trend-chart chartData="{{reportData.trendData}}"></c-health-trend-chart>
    </view>
  </view>

</view>