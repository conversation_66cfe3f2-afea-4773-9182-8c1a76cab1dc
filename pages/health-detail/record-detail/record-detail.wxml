<!-- pages/health/record-detail/record-detail.wxml -->
<view class="record-detail-container">
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <view wx:else class="record-content">
    <!-- 查看模式 -->
    <view wx:if="{{!isEditing}}" class="view-mode">
      <view class="record-header">
        <view class="record-title">
          {{record.status === 'vaccination' ? '防疫记录' : record.status === 'sick' ? '生病记录' : record.status === 'treatment' ? '治疗记录' : '死亡记录'}}
        </view>
        <view class="record-status {{record.status === 'vaccination' ? 'status-vaccination' : record.status === 'sick' ? 'status-sick' : record.status === 'treatment' ? 'status-treatment' : 'status-death'}}">
          {{record.status === 'vaccination' ? '防疫记录' : record.status === 'sick' ? '生病记录' : record.status === 'treatment' ? '治疗记录' : '死亡记录'}}
        </view>
      </view>
      
      <view class="record-meta">
        <view class="meta-item">
          <text class="label">记录日期</text>
          <text class="value">{{record.date}}</text>
        </view>
        <view class="meta-item">
          <text class="label">记录人</text>
          <text class="value">{{record.author}}</text>
        </view>
      </view>
      
      <view class="record-description">
        <view class="section-title">描述</view>
        <view class="description-content">{{record.description}}</view>
      </view>
      
      <view wx:if="{{record.images && record.images.length > 0}}" class="record-images">
        <view class="section-title">图片</view>
        <view class="images-container">
          <block wx:for="{{record.images}}" wx:key="index">
            <image class="image-item" src="{{item.url}}" mode="aspectFill"></image>
          </block>
        </view>
      </view>
      
      <view class="action-buttons">
        <button class="action-btn edit-btn" bindtap="onEdit">编辑</button>
        <button class="action-btn delete-btn" bindtap="onDelete">删除</button>
      </view>
    </view>
    
    <!-- 编辑模式 -->
    <view wx:else class="edit-mode">
      <view class="form-item">
        <text class="label">日期</text>
        <picker mode="date" value="{{record.date}}" bindchange="onDateChange">
          <view class="picker">
            {{record.date}}
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">记录类型</text>
        <view class="status-options">
          <view class="status-option {{record.status === 'vaccination' ? 'selected' : ''}}" data-status="vaccination" bindtap="onStatusChange">
            防疫记录
          </view>
          <view class="status-option {{record.status === 'sick' ? 'selected' : ''}}" data-status="sick" bindtap="onStatusChange">
            生病记录
          </view>
          <view class="status-option {{record.status === 'treatment' ? 'selected' : ''}}" data-status="treatment" bindtap="onStatusChange">
            治疗记录
          </view>
          <view class="status-option {{record.status === 'death' ? 'selected' : ''}}" data-status="death" bindtap="onStatusChange">
            死亡记录
          </view>
        </view>
      </view>



      <!-- 死亡状态特定字段 -->
      <view wx:if="{{record.status === 'death'}}" class="form-item status-specific-fields">
        <text class="label">死亡信息</text>
        <view class="death-fields">
          <view class="field-item">
            <text class="field-label">死亡原因</text>
            <picker mode="selector" range="{{deathCauseOptions}}" range-key="label" value="{{record.deathCauseIndex}}" data-field="deathCauseIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.deathCauseIndex !== undefined && deathCauseOptions[record.deathCauseIndex]}}">{{deathCauseOptions[record.deathCauseIndex].label}}</text>
                <text wx:else class="placeholder">请选择死亡原因</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">死亡时间</text>
            <picker mode="time" value="{{record.deathTime}}" data-field="deathTime" bindchange="onTimeChange">
              <view class="picker-display">
                <text wx:if="{{record.deathTime}}">{{record.deathTime}}</text>
                <text wx:else class="placeholder">请选择死亡时间</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">死亡症状</text>
            <textarea class="form-textarea" placeholder="请描述死亡前症状" value="{{record.deathSymptoms}}" data-field="deathSymptoms" bindinput="onInputChange"></textarea>
          </view>
          <view class="field-item">
            <text class="field-label">死亡数量</text>
            <input class="form-input" type="number" placeholder="请输入死亡数量" value="{{record.affectedCount}}" data-field="affectedCount" bindinput="onInputChange" />
          </view>
        </view>
      </view>

      <!-- 防疫记录特定字段 -->
      <view wx:if="{{record.status === 'vaccination'}}" class="form-item status-specific-fields">
        <text class="label">疫苗接种信息</text>
        <view class="vaccination-fields">
          <view class="field-item">
            <text class="field-label">疫苗类型</text>
            <picker mode="selector" range="{{vaccineTypeOptions}}" range-key="label" value="{{record.vaccineTypeIndex}}" data-field="vaccineTypeIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.vaccineTypeIndex !== undefined && vaccineTypeOptions[record.vaccineTypeIndex]}}">{{vaccineTypeOptions[record.vaccineTypeIndex].label}}</text>
                <text wx:else class="placeholder">请选择疫苗类型</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">疫苗批次</text>
            <input class="form-input" placeholder="请输入疫苗批次号" value="{{record.vaccineBatch}}" data-field="vaccineBatch" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">接种方式</text>
            <picker mode="selector" range="{{vaccinationMethodOptions}}" range-key="label" value="{{record.vaccinationMethodIndex}}" data-field="vaccinationMethodIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.vaccinationMethodIndex !== undefined && vaccinationMethodOptions[record.vaccinationMethodIndex]}}">{{vaccinationMethodOptions[record.vaccinationMethodIndex].label}}</text>
                <text wx:else class="placeholder">请选择接种方式</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">接种剂量</text>
            <input class="form-input" placeholder="请输入接种剂量（如：0.5ml）" value="{{record.vaccineDosage}}" data-field="vaccineDosage" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">接种数量</text>
            <input class="form-input" type="number" placeholder="请输入接种鹅只数量" value="{{record.vaccinatedCount}}" data-field="vaccinatedCount" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">下次接种日期</text>
            <picker mode="date" value="{{record.nextVaccinationDate}}" data-field="nextVaccinationDate" bindchange="onDateChange">
              <view class="picker-display">
                <text wx:if="{{record.nextVaccinationDate}}">{{record.nextVaccinationDate}}</text>
                <text wx:else class="placeholder">请选择下次接种日期</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">接种反应</text>
            <textarea class="form-textarea" placeholder="请描述接种后反应情况" value="{{record.vaccinationReaction}}" data-field="vaccinationReaction" bindinput="onInputChange"></textarea>
          </view>
        </view>
      </view>

      <!-- 生病记录特定字段 -->
      <view wx:if="{{record.status === 'sick'}}" class="form-item status-specific-fields">
        <text class="label">疾病信息</text>
        <view class="disease-fields">
          <view class="field-item">
            <text class="field-label">关联批次号</text>
            <input class="form-input" placeholder="请输入关联批次号" value="{{record.batchNumber}}" data-field="batchNumber" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">疾病类型</text>
            <picker mode="selector" range="{{diseaseTypeOptions}}" range-key="label" value="{{record.diseaseTypeIndex}}" data-field="diseaseTypeIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.diseaseTypeIndex !== undefined && diseaseTypeOptions[record.diseaseTypeIndex]}}">{{diseaseTypeOptions[record.diseaseTypeIndex].label}}</text>
                <text wx:else class="placeholder">请选择疾病类型</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">疾病症状</text>
            <textarea class="form-textarea" placeholder="请描述疾病症状" value="{{record.diseaseSymptoms}}" data-field="diseaseSymptoms" bindinput="onTextareaChange"></textarea>
          </view>
          <view class="field-item">
            <text class="field-label">严重程度</text>
            <picker mode="selector" range="{{severityOptions}}" range-key="label" value="{{record.severityIndex}}" data-field="severityIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.severityIndex !== undefined && severityOptions[record.severityIndex]}}">{{severityOptions[record.severityIndex].label}}</text>
                <text wx:else class="placeholder">请选择严重程度</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">发病日期</text>
            <picker mode="date" value="{{record.onsetDate}}" data-field="onsetDate" bindchange="onDateChange">
              <view class="picker-display">
                <text wx:if="{{record.onsetDate}}">{{record.onsetDate}}</text>
                <text wx:else class="placeholder">请选择发病日期</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">确诊日期</text>
            <picker mode="date" value="{{record.diagnosisDate}}" data-field="diagnosisDate" bindchange="onDateChange">
              <view class="picker-display">
                <text wx:if="{{record.diagnosisDate}}">{{record.diagnosisDate}}</text>
                <text wx:else class="placeholder">请选择确诊日期</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">患病数量</text>
            <input class="form-input" type="number" placeholder="请输入患病鹅只数量" value="{{record.sickCount}}" data-field="sickCount" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">是否传染</text>
            <picker mode="selector" range="{{contagiousOptions}}" range-key="label" value="{{record.contagiousIndex}}" data-field="contagiousIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.contagiousIndex !== undefined && contagiousOptions[record.contagiousIndex]}}">{{contagiousOptions[record.contagiousIndex].label}}</text>
                <text wx:else class="placeholder">请选择是否传染</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">是否隔离</text>
            <picker mode="selector" range="{{isolationOptions}}" range-key="label" value="{{record.isolationIndex}}" data-field="isolationIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.isolationIndex !== undefined && isolationOptions[record.isolationIndex]}}">{{isolationOptions[record.isolationIndex].label}}</text>
                <text wx:else class="placeholder">请选择是否隔离</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">严重程度</text>
            <picker mode="selector" range="{{severityOptions}}" range-key="label" value="{{record.severityIndex}}" data-field="severityIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.severityIndex !== undefined && severityOptions[record.severityIndex]}}">{{severityOptions[record.severityIndex].label}}</text>
                <text wx:else class="placeholder">请选择严重程度</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">发病日期</text>
            <picker mode="date" value="{{record.onsetDate}}" data-field="onsetDate" bindchange="onDateFieldChange">
              <view class="picker-display">
                <text wx:if="{{record.onsetDate}}">{{record.onsetDate}}</text>
                <text wx:else class="placeholder">请选择发病日期</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">患病数量</text>
            <input class="form-input" type="number" placeholder="请输入患病数量" value="{{record.sickCount}}" data-field="sickCount" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">传染性评估</text>
            <picker mode="selector" range="{{contagiousOptions}}" range-key="label" value="{{record.contagiousIndex}}" data-field="contagiousIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.contagiousIndex !== undefined && contagiousOptions[record.contagiousIndex]}}">{{contagiousOptions[record.contagiousIndex].label}}</text>
                <text wx:else class="placeholder">请选择传染性评估</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">隔离措施</text>
            <picker mode="selector" range="{{isolationOptions}}" range-key="label" value="{{record.isolationIndex}}" data-field="isolationIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.isolationIndex !== undefined && isolationOptions[record.isolationIndex]}}">{{isolationOptions[record.isolationIndex].label}}</text>
                <text wx:else class="placeholder">请选择隔离措施</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">病症描述</text>
            <textarea class="form-textarea" placeholder="请详细描述病症" value="{{record.diseaseSymptoms}}" data-field="diseaseSymptoms" bindinput="onTextareaChange"></textarea>
          </view>
        </view>
      </view>

      <!-- 治疗记录特定字段 -->
      <view wx:if="{{record.status === 'treatment'}}" class="form-item status-specific-fields">
        <text class="label">治疗信息</text>
        <view class="treatment-fields">
          <view class="field-item">
            <text class="field-label">关联生病记录ID</text>
            <input class="form-input" placeholder="请输入相关生病记录ID" value="{{record.relatedSickRecordId}}" data-field="relatedSickRecordId" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">治疗方案</text>
            <picker mode="selector" range="{{treatmentOptions}}" range-key="label" value="{{record.treatmentIndex}}" data-field="treatmentIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.treatmentIndex !== undefined && treatmentOptions[record.treatmentIndex]}}">{{treatmentOptions[record.treatmentIndex].label}}</text>
                <text wx:else class="placeholder">请选择治疗方案</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">药物名称</text>
            <input class="form-input" placeholder="请输入使用的药物名称" value="{{record.medicationName}}" data-field="medicationName" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">用药剂量</text>
            <input class="form-input" placeholder="请输入用药剂量" value="{{record.treatmentDosage}}" data-field="treatmentDosage" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">治疗周期</text>
            <input class="form-input" placeholder="请输入治疗周期" value="{{record.treatmentDuration}}" data-field="treatmentDuration" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">治疗数量</text>
            <input class="form-input" type="number" placeholder="请输入治疗数量" value="{{record.treatmentCount}}" data-field="treatmentCount" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">治疗效果</text>
            <picker mode="selector" range="{{treatmentEffectOptions}}" range-key="label" value="{{record.treatmentEffectIndex}}" data-field="treatmentEffectIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{record.treatmentEffectIndex !== undefined && treatmentEffectOptions[record.treatmentEffectIndex]}}">{{treatmentEffectOptions[record.treatmentEffectIndex].label}}</text>
                <text wx:else class="placeholder">请选择治疗效果</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">治愈数量</text>
            <input class="form-input" type="number" placeholder="请输入治愈数量" value="{{record.curedCount}}" data-field="curedCount" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">治疗费用(元)</text>
            <input class="form-input" type="digit" placeholder="请输入治疗费用" value="{{record.treatmentCost}}" data-field="treatmentCost" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">复查日期</text>
            <picker mode="date" value="{{record.followUpDate}}" data-field="followUpDate" bindchange="onDateChange">
              <view class="picker-display">
                <text wx:if="{{record.followUpDate}}">{{record.followUpDate}}</text>
                <text wx:else class="placeholder">请选择复查日期</text>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="label">描述</text>
        <textarea class="form-textarea" placeholder="请输入描述" value="{{record.description}}" data-field="description" bindinput="onTextareaChange"></textarea>
      </view>
      
      <view class="form-item">
        <text class="label">图片</text>
        <view class="images-upload">
          <block wx:for="{{record.images}}" wx:key="index">
            <view class="uploaded-image">
              <image src="{{item.url}}" mode="aspectFill"></image>
              <view class="delete-image" bindtap="onDeleteImage" data-index="{{index}}">✕</view>
            </view>
          </block>
          <view class="upload-button" bindtap="onChooseImage">
            <image src="/images/icon_camera.png" mode="aspectFit"></image>
            <text>上传图片</text>
          </view>
        </view>
      </view>
      
      <view class="form-buttons">
        <button class="form-btn cancel-btn" bindtap="onCancel">取消</button>
        <button class="form-btn save-btn" bindtap="onSave">保存</button>
      </view>
    </view>
  </view>
</view>