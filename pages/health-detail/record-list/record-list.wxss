/* pages/health/record-list/record-list.wxss */
.record-list-container {
  min-height: 100vh;
  background-color: #f5f6f8;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #666666;
  border-radius: 10rpx;
}

.filter-item.active {
  background-color: #0066cc;
  color: #ffffff;
  font-weight: bold;
}

/* 记录容器 */
.records-container {
  padding-bottom: 120rpx;
}

/* 记录项 */
.record-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
  margin-right: 20rpx;
}

.record-status {
  font-size: 24rpx;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

.status-healthy {
  background-color: #e6f9f8;
  color: #00cc99;
}

.status-sick {
  background-color: #ffe6e6;
  color: #ff3333;
}

.status-treated {
  background-color: #fff3e6;
  color: #ff9900;
}

.record-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.record-date {
  font-size: 24rpx;
  color: #999999;
}

.record-author {
  font-size: 24rpx;
  color: #999999;
}

.record-summary {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.empty-btn {
  background-color: #0066cc;
  color: #ffffff;
  border: none;
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more,
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}

/* 添加按钮 */
.add-button {
  position: fixed;
  bottom: 50rpx;
  right: 50rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #0066cc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 102, 204, 0.3);
}

.add-button image {
  width: 50rpx;
  height: 50rpx;
}