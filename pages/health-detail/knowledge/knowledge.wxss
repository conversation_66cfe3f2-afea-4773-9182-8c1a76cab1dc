/* pages/health/knowledge/knowledge.wxss */
.knowledge-container {
  min-height: 100vh;
  background-color: #f5f6f8;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 0 20rpx;
}

.search-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  background-color: #0066cc;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

/* 分类筛选 */
.category-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.category-list {
  display: inline-block;
}

.category-item {
  display: inline-block;
  padding: 16rpx 24rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  font-weight: 500;
}

.category-item.active {
  background: linear-gradient(135deg, #0066CC 0%, #0099CC 100%);
  color: #ffffff;
  font-weight: bold;
  border: 2rpx solid rgba(0, 102, 204, 0.3);
  box-shadow: 0 4rpx 15rpx rgba(0, 102, 204, 0.3);
  transform: translateY(-2rpx);
}

/* 文章列表 */
.articles-container {
  padding-bottom: 20rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx 0;
  font-size: 28rpx;
  color: #999999;
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.article-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.article-category {
  font-size: 24rpx;
  color: #0066cc;
  background-color: #e6f4ff;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
}

.article-date {
  font-size: 24rpx;
  color: #999999;
}

.article-read-count {
  font-size: 24rpx;
  color: #999999;
}

.article-summary {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
}

/* 加载更多 */
.load-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
}