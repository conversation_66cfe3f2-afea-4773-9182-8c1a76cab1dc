/* pages/health/knowledge/detail/detail.wxss */
.container {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding-bottom: 60rpx;
  position: relative;
  overflow-x: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 102, 204, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 204, 153, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .article-header {
    padding: 28rpx 20rpx;
    margin: 0 16rpx 20rpx;
    border-radius: 24rpx;
  }

  .article-title {
    font-size: 36rpx;
    line-height: 1.3;
  }

  .meta-item {
    font-size: 20rpx;
  }

  .article-content {
    padding: 28rpx 20rpx;
    margin: 0 16rpx 20rpx;
    border-radius: 24rpx;
  }

  .article-tags-section {
    padding: 24rpx 28rpx;
    margin: 0 16rpx 20rpx;
    border-radius: 20rpx;
  }
}

/* 文章头部 */
.article-header {
  background: linear-gradient(135deg, #0066CC 0%, #00CC99 100%);
  padding: 32rpx 40rpx 24rpx;
  margin: 0 20rpx 20rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 102, 204, 0.15);
  position: relative;
  overflow: hidden;
  text-align: center;
}

.article-header::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 150%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/><circle cx="20" cy="20" r="20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.3"/><circle cx="80" cy="80" r="30" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="0.4"/></svg>') no-repeat;
  background-size: 400rpx 400rpx;
  z-index: 0;
}

.article-title {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  line-height: 1.4;
  margin-bottom: 20rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.article-meta {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
  padding: 0;
  position: relative;
  z-index: 1;
  justify-content: center;
  margin-top: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.meta-emoji {
  font-size: 20rpx;
  margin-right: 6rpx;
  line-height: 1;
}

/* 文章内容 */
.article-content {
  background-color: white;
  padding: 40rpx;
  margin: 0 20rpx 24rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  line-height: 1.8;
  font-size: 28rpx;
  color: #2c3e50;
  position: relative;

  border: 1rpx solid rgba(0, 102, 204, 0.08);
}

/* 文章内容排版优化 */
.article-content h3 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin: 48rpx 0 24rpx 0;
  padding-left: 20rpx;
  border-left: 6rpx solid #0066CC;
  background: linear-gradient(90deg, rgba(0, 102, 204, 0.05) 0%, transparent 100%);
  padding: 16rpx 0 16rpx 20rpx;
  border-radius: 0 12rpx 12rpx 0;
}

.article-content h4 {
  font-size: 34rpx;
  font-weight: 600;
  color: #34495e;
  margin: 32rpx 0 16rpx 0;
  padding-left: 16rpx;
  border-left: 4rpx solid #00CC99;
  position: relative;
}

.article-content h4::before {
  content: '•';
  color: #00CC99;
  font-size: 24rpx;
  position: absolute;
  left: -8rpx;
  top: 50%;
  transform: translateY(-50%);
}

.article-content p {
  margin: 16rpx 0;
  line-height: 1.7;
  text-align: justify;
  color: #2c3e50;
  font-size: 26rpx;
}

.article-content strong {
  color: #0066CC;
  font-weight: 600;
  background: rgba(0, 102, 204, 0.05);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

.article-content p:first-of-type {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20rpx;
  border-radius: 16rpx;
  border: 1rpx solid #dee2e6;
  margin-bottom: 24rpx;
  position: relative;
}

.article-content p:first-of-type::before {
  content: '💡';
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  font-size: 24rpx;
  opacity: 0.6;
}
