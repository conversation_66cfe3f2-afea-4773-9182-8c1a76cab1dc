// pages/health/knowledge/knowledge.js
const api = require('../../../utils/api.js');

Page({
  data: {
    categories: [],
    activeCategory: 'all',
    articles: [],
    loading: true,
    searchKeyword: '',
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad: function (options) {
    // 页面加载时获取分类和文章列表
    this.loadCategories();
    this.loadArticles();
  },

  onShow: function () {
    // 页面显示
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadArticles(() => {
        wx.stopPullDownRefresh();
      });
    });
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore) {
      this.loadMore();
    }
  },

  // 加载分类 - 使用统一数据源
  loadCategories: function () {
    try {
      // 引入统一的知识库数据
      const { getCategories } = require('../../../utils/knowledge-data.js');

      this.setData({
        categories: categories
      });
    } catch (error) {
      console.error('加载分类失败', error);
      // 使用备用数据
      const categories = [
        { id: 'all', name: '全部' },
        { id: 'disease', name: '疾病防治' },
        { id: 'feed', name: '饲料营养' },
        { id: 'breed', name: '品种繁育' },
        { id: 'manage', name: '饲养管理' }
      ];
      this.setData({ categories: categories });
    }
  },

  // 加载文章列表 - 使用统一数据源
  loadArticles: function (callback) {
    const { pageNum, pageSize, activeCategory, searchKeyword } = this.data;
    
    // 显示加载状态
    if (pageNum === 1) {
      this.setData({
        loading: true
      });
    }
    
    // 模拟API调用
    setTimeout(() => {
      try {
        // 引入统一的知识库数据
        const { getArticlesByCategory, searchArticles } = require('../../../utils/knowledge-data.js');
        
        let filteredArticles;

        // 如果有搜索关键词，使用搜索功能
        if (searchKeyword && searchKeyword.trim()) {
          filteredArticles = searchArticles(searchKeyword.trim(), activeCategory);
        } else {
          // 否则按分类获取文章
          filteredArticles = getArticlesByCategory(activeCategory);
        }
        
        // 模拟分页逻辑
        const startIndex = (pageNum - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const pageArticles = filteredArticles.slice(startIndex, endIndex);
        const hasMore = endIndex < filteredArticles.length;
        
        this.setData({
          articles: pageNum === 1 ? pageArticles : this.data.articles.concat(pageArticles),
          hasMore: hasMore,
          loading: false
        });callback && callback();
      } catch (error) {
        console.error('加载文章失败', error);
        this.setData({
          articles: [],
          hasMore: false,
          loading: false
        });
        callback && callback();
      }
    }, 500);
  },

  // 加载更多
  loadMore: function () {
    this.setData({
      pageNum: this.data.pageNum + 1
    }, () => {
      this.loadArticles();
    });
  },

  // 切换分类
  onCategoryChange: function (e) {
    const category = e.currentTarget.dataset.category;

    // 如果点击的是当前激活的分类，不做任何操作
    if (category === this.data.activeCategory) {
      return;
    }

    this.setData({
      activeCategory: category,
      pageNum: 1,
      hasMore: true,
      loading: true
    }, () => {
      // 添加一个短暂的延迟，让用户看到切换效果
      setTimeout(() => {
        this.loadArticles();
      }, 300);
    });
  },

  // 搜索输入
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch: function () {
    this.setData({
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadArticles();
    });
  },

  // 查看文章详情
  onViewArticle: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/health/knowledge/detail/detail?id=${id}`
    });
  }
});