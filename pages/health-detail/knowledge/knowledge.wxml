<!-- pages/health/knowledge/knowledge.wxml -->
<view class="knowledge-container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-container">
      <image class="search-icon" src="/images/icon_search.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索知识文章" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
    </view>
    <button class="search-btn" bindtap="onSearch">搜索</button>
  </view>
  
  <!-- 分类筛选 -->
  <scroll-view class="category-scroll" scroll-x>
    <view class="category-list">
      <block wx:for="{{categories}}" wx:key="id">
        <view class="category-item {{activeCategory === item.id ? 'active' : ''}}" data-category="{{item.id}}" bindtap="onCategoryChange">
          {{item.name}}
        </view>
      </block>
    </view>
  </scroll-view>
  
  <!-- 文章列表 -->
  <view class="articles-container">
    <!-- 加载状态 -->
    <view wx:if="{{loading && articles.length === 0}}" class="loading-state">
      <text>加载中...</text>
    </view>

    <!-- 文章列表 -->
    <block wx:if="{{!loading || articles.length > 0}}" wx:for="{{articles}}" wx:key="id">
      <view class="article-item" bindtap="onViewArticle" data-id="{{item.id}}">
        <view class="article-title">{{item.title}}</view>
        <view class="article-meta">
          <view class="article-category">{{item.categoryName}}</view>
          <view class="article-date">{{item.publishDate}}</view>
          <view class="article-read-count">阅读 {{item.readCount}}</view>
        </view>
        <view class="article-summary">{{item.summary}}</view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{articles.length === 0 && !loading}}" class="empty-state">
      <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无相关文章</text>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && articles.length > 0}}" class="load-more">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view wx:elif="{{!hasMore && articles.length > 0}}" class="no-more">
      <text>没有更多数据了</text>
    </view>
  </view>
</view>