<!-- pages/health/material-detail/material-detail.wxml -->
<view class="material-detail-container">
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <view wx:else class="material-content">
    <view class="edit-mode">
      <view class="form-item">
        <text class="label">记录日期</text>
        <picker mode="date" value="{{material.recordDate}}" bindchange="onDateChange">
          <view class="picker">
            {{material.recordDate}}
          </view>
        </picker>
      </view>


      
      <!-- 物料类型标签页 -->
      <view class="form-item">
        <text class="label">物料类型</text>
        <view class="category-tabs">
          <view class="tab-item {{material.category === 'feed' ? 'active' : ''}}" data-category="feed" bindtap="onCategoryChange">
            <text class="tab-text">饲料管理</text>
          </view>
          <view class="tab-item {{material.category === 'medicine' ? 'active' : ''}}" data-category="medicine" bindtap="onCategoryChange">
            <text class="tab-text">药品管理</text>
          </view>
          <view class="tab-item {{material.category === 'other' ? 'active' : ''}}" data-category="other" bindtap="onCategoryChange">
            <text class="tab-text">其他物料</text>
          </view>
        </view>
      </view>

      <!-- 饲料管理特定字段 -->
      <view wx:if="{{material.category === 'feed'}}" class="form-item category-specific-fields">
        <text class="label">饲料信息</text>
        <view class="feed-fields">
          <view class="field-item">
            <text class="field-label">饲料名称 <text class="required">*</text></text>
            <input class="form-input" placeholder="请输入饲料名称（如：雏鹅全价饲料）" value="{{material.name}}" data-field="name" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">饲料类型</text>
            <picker mode="selector" range="{{feedTypeOptions}}" range-key="label" value="{{material.feedTypeIndex}}" data-field="feedTypeIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{material.feedTypeIndex !== undefined && feedTypeOptions[material.feedTypeIndex]}}">{{feedTypeOptions[material.feedTypeIndex].label}}</text>
                <text wx:else class="placeholder">请选择饲料类型</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">规格包装 <text class="required">*</text></text>
            <input class="form-input" placeholder="请输入规格（如：25kg/袋）" value="{{material.spec}}" data-field="spec" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">库存数量 <text class="required">*</text></text>
            <input class="form-input" type="number" placeholder="请输入库存数量" value="{{material.stock}}" data-field="stock" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">供应商信息</text>
            <input class="form-input" placeholder="请输入供应商名称" value="{{material.supplier}}" data-field="supplier" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">单价信息</text>
            <input class="form-input" type="digit" placeholder="请输入单价（元）" value="{{material.unitPrice}}" data-field="unitPrice" bindinput="onInputChange" />
          </view>
        </view>
      </view>

      <!-- 药品管理特定字段 -->
      <view wx:elif="{{material.category === 'medicine'}}" class="form-item category-specific-fields">
        <text class="label">药品信息</text>
        <view class="medicine-fields">
          <view class="field-item">
            <text class="field-label">药品名称 <text class="required">*</text></text>
            <input class="form-input" placeholder="请输入药品名称（如：青霉素注射液）" value="{{material.name}}" data-field="name" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">药品类型</text>
            <picker mode="selector" range="{{medicineTypeOptions}}" range-key="label" value="{{material.medicineTypeIndex}}" data-field="medicineTypeIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{material.medicineTypeIndex !== undefined && medicineTypeOptions[material.medicineTypeIndex]}}">{{medicineTypeOptions[material.medicineTypeIndex].label}}</text>
                <text wx:else class="placeholder">请选择药品类型</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">规格包装 <text class="required">*</text></text>
            <input class="form-input" placeholder="请输入规格（如：100ml/瓶）" value="{{material.spec}}" data-field="spec" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">库存数量 <text class="required">*</text></text>
            <input class="form-input" type="number" placeholder="请输入库存数量" value="{{material.stock}}" data-field="stock" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">生产厂家</text>
            <input class="form-input" placeholder="请输入生产厂家" value="{{material.manufacturer}}" data-field="manufacturer" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">有效期</text>
            <picker mode="date" value="{{material.expiryDate}}" data-field="expiryDate" bindchange="onDateFieldChange">
              <view class="picker-display">
                <text wx:if="{{material.expiryDate}}">{{material.expiryDate}}</text>
                <text wx:else class="placeholder">请选择有效期</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">批号信息</text>
            <input class="form-input" placeholder="请输入药品批号" value="{{material.batchNumber}}" data-field="batchNumber" bindinput="onInputChange" />
          </view>
        </view>
      </view>

      <!-- 其他物料特定字段 -->
      <view wx:elif="{{material.category === 'other'}}" class="form-item category-specific-fields">
        <text class="label">物料信息</text>
        <view class="other-fields">
          <view class="field-item">
            <text class="field-label">物料名称 <text class="required">*</text></text>
            <input class="form-input" placeholder="请输入物料名称" value="{{material.name}}" data-field="name" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">物料用途</text>
            <picker mode="selector" range="{{otherTypeOptions}}" range-key="label" value="{{material.otherTypeIndex}}" data-field="otherTypeIndex" bindchange="onPickerChange">
              <view class="picker-display">
                <text wx:if="{{material.otherTypeIndex !== undefined && otherTypeOptions[material.otherTypeIndex]}}">{{otherTypeOptions[material.otherTypeIndex].label}}</text>
                <text wx:else class="placeholder">请选择物料用途</text>
              </view>
            </picker>
          </view>
          <view class="field-item">
            <text class="field-label">规格信息 <text class="required">*</text></text>
            <input class="form-input" placeholder="请输入规格信息" value="{{material.spec}}" data-field="spec" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">库存数量 <text class="required">*</text></text>
            <input class="form-input" type="number" placeholder="请输入库存数量" value="{{material.stock}}" data-field="stock" bindinput="onInputChange" />
          </view>
          <view class="field-item">
            <text class="field-label">供应商信息</text>
            <input class="form-input" placeholder="请输入供应商名称" value="{{material.supplier}}" data-field="supplier" bindinput="onInputChange" />
          </view>
        </view>
      </view>

      <!-- 通用备注字段 -->
      <view class="form-item">
        <text class="label">备注信息</text>
        <textarea class="form-textarea" placeholder="请输入备注信息（可选）" value="{{material.notes}}" data-field="notes" bindinput="onInputChange"></textarea>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button class="action-btn cancel-btn" bindtap="onCancel">取消</button>
        <button class="action-btn save-btn" bindtap="onSave" loading="{{saving}}">{{saving ? '保存中...' : '保存'}}</button>
      </view>
    </view>
  </view>
</view>
