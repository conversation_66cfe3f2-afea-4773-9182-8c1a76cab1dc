/**
 * 物料详情页面
 * 用于查看和编辑物料信息
 */

Page({
  data: {
    material: {
      id: '',
      name: '',
      category: '',
      spec: '',
      stock: '',
      supplier: '',
      notes: '',
      recordDate: '',
      feedType: '',
      unitPrice: '',
      medicineType: '',
      manufacturer: '',
      expiryDate: '',
      batchNumber: '',
      otherType: ''
    },
    
    // 分类选项
    categoryOptions: [
      { value: 'feed', label: '饲料' },
      { value: 'medicine', label: '药品' },
      { value: 'other', label: '其他' }
    ],
    
    // 饲料类型选项
    feedTypeOptions: [
      { value: 'concentrate', label: '精料' },
      { value: 'roughage', label: '粗料' },
      { value: 'supplement', label: '添加剂' }
    ],
    
    // 药品类型选项
    medicineTypeOptions: [
      { value: 'antibiotic', label: '抗生素' },
      { value: 'vaccine', label: '疫苗' },
      { value: 'vitamin', label: '维生素' },
      { value: 'other', label: '其他' }
    ],
    
    // 其他类型选项
    otherTypeOptions: [
      { value: 'equipment', label: '设备' },
      { value: 'tool', label: '工具' },
      { value: 'material', label: '材料' }
    ],
    
    // 编辑状态
    isEdit: false,
    saving: false,
    
    // 表单索引
    feedTypeIndex: 0,
    medicineTypeIndex: 0,
    otherTypeIndex: 0
  },

  onLoad: function(options) {
    const { id, category } = options;
    
    if (id) {
      // 编辑模式
      this.loadMaterialDetail(id);
      this.setData({ isEdit: true });
    } else if (category) {
      // 新增模式，设置默认分类
      this.setData({
        'material.category': category,
        'material.recordDate': this.formatDate(new Date())
      });
    } else {
      // 默认新增模式
      this.setData({
        'material.recordDate': this.formatDate(new Date())
      });
    }
  },

  /**
   * 加载物料详情
   */
  loadMaterialDetail: function(id) {
    try {
      const existingMaterials = wx.getStorageSync('materialList') || [];
      const material = existingMaterials.find(item => item.id == id);
      
      if (material) {
        // 设置分类索引
        let feedTypeIndex = 0;
        let medicineTypeIndex = 0;
        let otherTypeIndex = 0;
        
        if (material.category === 'feed' && material.feedType) {
          feedTypeIndex = this.data.feedTypeOptions.findIndex(
            option => option.value === material.feedType
          );
        } else if (material.category === 'medicine' && material.medicineType) {
          medicineTypeIndex = this.data.medicineTypeOptions.findIndex(
            option => option.value === material.medicineType
          );
        } else if (material.category === 'other' && material.otherType) {
          otherTypeIndex = this.data.otherTypeOptions.findIndex(
            option => option.value === material.otherType
          );
        }
        
        this.setData({
          material: material,
          feedTypeIndex: Math.max(0, feedTypeIndex),
          medicineTypeIndex: Math.max(0, medicineTypeIndex),
          otherTypeIndex: Math.max(0, otherTypeIndex)
        });
      } else {
        wx.showToast({
          title: '物料不存在',
          icon: 'none'
        });
        setTimeout(() => wx.navigateBack(), 1500);
      }
    } catch (error) {
      console.error('加载物料详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分类变化
   */
  onCategoryChange: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      'material.category': category,
      'material.feedType': '',
      'material.medicineType': '',
      'material.otherType': ''
    });
  },

  /**
   * 饲料类型变化
   */
  onFeedTypeChange: function(e) {
    const index = e.detail.value;
    this.setData({
      feedTypeIndex: index,
      'material.feedType': this.data.feedTypeOptions[index].value
    });
  },

  /**
   * 药品类型变化
   */
  onMedicineTypeChange: function(e) {
    const index = e.detail.value;
    this.setData({
      medicineTypeIndex: index,
      'material.medicineType': this.data.medicineTypeOptions[index].value
    });
  },

  /**
   * 其他类型变化
   */
  onOtherTypeChange: function(e) {
    const index = e.detail.value;
    this.setData({
      otherTypeIndex: index,
      'material.otherType': this.data.otherTypeOptions[index].value
    });
  },

  /**
   * 输入变化
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`material.${field}`]: e.detail.value
    });
  },

  /**
   * Picker选择器变化
   */
  onPickerChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`material.${field}`]: value
    });
  },

  /**
   * 日期字段变化（用于有效期等）
   */
  onDateFieldChange: function(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`material.${field}`]: e.detail.value
    });
  },

  /**
   * 日期选择
   */
  onDateChange: function(e) {
    this.setData({
      'material.recordDate': e.detail.value
    });
  },

  /**
   * 保存物料
   */
  onSave: function() {
    if (this.data.saving) return;
    
    if (!this.validateForm()) {
      return;
    }
    
    this.setData({ saving: true });
    
    try {
      this.saveMaterialRecord(this.data.material);
    } catch (error) {
      console.error('保存失败:', error);
      this.setData({ saving: false });
      
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 表单验证
   */
  validateForm: function() {
    const material = this.data.material;
    
    if (!material.name || material.name.trim() === '') {
      wx.showToast({ title: '请输入物料名称', icon: 'none' });
      return false;
    }

    if (!material.category) {
      wx.showToast({ title: '请选择物料分类', icon: 'none' });
      return false;
    }

    if (!material.spec || material.spec.trim() === '') {
      wx.showToast({ title: '请输入规格型号', icon: 'none' });
      return false;
    }

    if (!material.stock || material.stock.trim() === '') {
      wx.showToast({ title: '请输入库存数量', icon: 'none' });
      return false;
    }

    // 验证库存数量是否为有效数字
    const stockNum = parseInt(material.stock);
    if (isNaN(stockNum) || stockNum < 0) {
      wx.showToast({ title: '库存数量必须为有效数字', icon: 'none' });
      return false;
    }

    return true;
  },

  /**
   * 获取物料状态
   */
  getMaterialStatus: function(stock) {
    const stockNum = parseInt(stock);
    if (stockNum <= 0) {
      return 'danger';
    } else if (stockNum < 10) {
      return 'warning';
    } else {
      return 'normal';
    }
  },

  /**
   * 保存物料记录
   */
  saveMaterialRecord: function(material) {
    try {
      // 生成新的物料记录
      const newMaterial = {
        id: this.data.isEdit ? material.id : Date.now(),
        name: material.name.trim(),
        category: material.category,
        spec: material.spec.trim(),
        stock: parseInt(material.stock) || 0,
        supplier: material.supplier ? material.supplier.trim() : '',
        notes: material.notes ? material.notes.trim() : '',
        recordDate: material.recordDate,
        status: this.getMaterialStatus(material.stock),
        createTime: material.createTime || new Date().toISOString(),
        updateTime: new Date().toISOString()
      };

      // 根据类别添加特有字段
      if (material.category === 'feed') {
        const feedTypeOptions = this.data.feedTypeOptions;
        newMaterial.feedType = material.feedTypeIndex !== undefined && feedTypeOptions[material.feedTypeIndex] 
          ? feedTypeOptions[material.feedTypeIndex].value 
          : '';
        newMaterial.unitPrice = material.unitPrice ? parseFloat(material.unitPrice) : 0;
      } else if (material.category === 'medicine') {
        const medicineTypeOptions = this.data.medicineTypeOptions;
        newMaterial.medicineType = material.medicineTypeIndex !== undefined && medicineTypeOptions[material.medicineTypeIndex] 
          ? medicineTypeOptions[material.medicineTypeIndex].value 
          : '';
        newMaterial.manufacturer = material.manufacturer ? material.manufacturer.trim() : '';
        newMaterial.expiryDate = material.expiryDate || '';
        newMaterial.batchNumber = material.batchNumber ? material.batchNumber.trim() : '';
      } else if (material.category === 'other') {
        const otherTypeOptions = this.data.otherTypeOptions;
        newMaterial.otherType = material.otherTypeIndex !== undefined && otherTypeOptions[material.otherTypeIndex] 
          ? otherTypeOptions[material.otherTypeIndex].value 
          : '';
      }

      // 保存到本地存储
      const existingMaterials = wx.getStorageSync('materialList') || [];
      
      if (this.data.isEdit) {
        // 编辑模式：更新现有记录
        const index = existingMaterials.findIndex(item => item.id == material.id);
        if (index !== -1) {
          existingMaterials[index] = newMaterial;
        }
      } else {
        // 新增模式：添加新记录
        existingMaterials.push(newMaterial);
      }
      
      wx.setStorageSync('materialList', existingMaterials);

      this.setData({ saving: false });

      wx.showToast({
        title: this.data.isEdit ? '更新成功' : '保存成功',
        icon: 'success',
        duration: 2000
      });

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);

    } catch (error) {
      console.error('保存物料记录失败:', error);
      this.setData({ saving: false });

      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 删除物料
   */
  onDelete: function() {
    if (!this.data.isEdit) return;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个物料记录吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          this.deleteMaterialRecord();
        }
      }
    });
  },

  /**
   * 删除物料记录
   */
  deleteMaterialRecord: function() {
    try {
      const materialId = this.data.material.id;
      const existingMaterials = wx.getStorageSync('materialList') || [];
      
      const filteredMaterials = existingMaterials.filter(item => item.id != materialId);
      wx.setStorageSync('materialList', filteredMaterials);

      wx.showToast({
        title: '删除成功',
        icon: 'success',
        duration: 2000
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 2000);

    } catch (error) {
      console.error('删除物料记录失败:', error);
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化日期
   */
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
});
