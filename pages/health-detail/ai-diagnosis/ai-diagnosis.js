// pages/health/ai-diagnosis/ai-diagnosis.js
Page({
  data: {
    symptoms: '',
    uploadedImages: [],
    diagnosisResult: null,
    isDiagnosing: false
  },

  onLoad: function (options) {
    // 页面加载
  },

  onShow: function () {
    // 页面显示
  },

  // 输入症状描述
  onSymptomInput: function (e) {
    this.setData({
      symptoms: e.detail.value
    });
  },

  // 选择图片
  onChooseImage: function () {
    const that = this;

    // 显示选择来源的弹窗
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: function(res) {
        let sourceType = [];
        if (res.tapIndex === 0) {
          sourceType = ['album'];
          that.chooseImageFromSource(sourceType);
        } else if (res.tapIndex === 1) {
          // 拍照需要相机权限
          wx.authorize({
            scope: 'scope.camera',
            success: function() {
              sourceType = ['camera'];
              that.chooseImageFromSource(sourceType);
            },
            fail: function() {
              wx.showModal({
                title: '需要相机权限',
                content: '使用拍照功能需要授权相机权限，请在设置中开启',
                showCancel: true,
                confirmText: '去设置',
                success: function(modalRes) {
                  if (modalRes.confirm) {
                    wx.openSetting();
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  // 从指定来源选择图片
  chooseImageFromSource: function(sourceType) {
    const that = this;
    wx.chooseImage({
      count: 3,
      sizeType: ['compressed'],
      sourceType: sourceType,
      success: function (res) {
        const tempFiles = res.tempFiles;
        const uploadedImages = that.data.uploadedImages;

        // 限制最多3张图片
        tempFiles.forEach(file => {
          if (uploadedImages.length < 3) {
            uploadedImages.push({
              url: file.path,
              size: file.size
            });
          }
        });

        that.setData({
          uploadedImages: uploadedImages
        });
      },
      fail: function(err) {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('选择图片失败', err); } catch(_) {}
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除已上传的图片
  onDeleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const uploadedImages = this.data.uploadedImages;
    uploadedImages.splice(index, 1);
    
    this.setData({
      uploadedImages: uploadedImages
    });
  },

  // 开始诊断
  onStartDiagnosis: function () {
    const { symptoms, uploadedImages } = this.data;
    
    if (!symptoms && uploadedImages.length === 0) {
      wx.showToast({
        title: '请填写症状或上传图片',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      isDiagnosing: true
    });
    
    // 调用真实的AI诊断API
    const { health } = require('../../../utils/api');
    
    health.aiDiagnosis({
      symptoms: symptoms,
      images: uploadedImages.map(img => img.path)
    }).then(res => {
      if (res.success) {
        // 转换API响应格式为页面需要的格式
        const result = {
          disease: res.data.diagnosis,
          confidence: Math.round(res.data.confidence * 100) + '%',
          description: `基于症状分析的诊断结果，置信度：${Math.round(res.data.confidence * 100)}%`,
          suggestions: res.data.suggestions,
          medications: res.data.medications || []
        };
        
        this.setData({
          diagnosisResult: result,
          isDiagnosing: false
        });
        
        wx.showToast({
          title: '诊断完成',
          icon: 'success'
        });
        
        // 保存诊断记录
        this.saveDiagnosisRecord(result);
      } else {
        this.setData({
          isDiagnosing: false
        });
        // 显示基础诊断建议
        this.showBasicDiagnosisAdvice();
      }
    }).catch(err => {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('AI诊断请求失败', err); } catch(_) {}
      this.setData({
        isDiagnosing: false
      });
      // 显示基础诊断建议
      this.showBasicDiagnosisAdvice();
    });
  },

  // 显示基础诊断建议
  showBasicDiagnosisAdvice: function() {
    const symptoms = this.data.symptoms.toLowerCase();
    let diagnosis = '基础诊断建议';
    let suggestions = ['请继续观察症状变化', '保持环境清洁', '必要时联系兽医'];
    
    if (symptoms.includes('咳嗽') || symptoms.includes('呼吸困难')) {
      diagnosis = '疑似呼吸道疾病';
      suggestions = ['改善鹅舍通风', '检查空气质量', '考虑使用抗生素治疗', '隔离观察'];
    } else if (symptoms.includes('腹泻') || symptoms.includes('拉稀')) {
      diagnosis = '疑似消化道疾病';
      suggestions = ['检查饲料质量', '调整饮食', '补充益生菌', '保持饮水清洁'];
    } else if (symptoms.includes('精神萎靡') || symptoms.includes('食欲不振')) {
      diagnosis = '疑似一般性疾病';
      suggestions = ['观察体温变化', '加强营养', '改善饲养环境', '及时就医'];
    }
    
    const result = {
      disease: diagnosis,
      confidence: '60%',
      description: '基于症状描述的基础诊断建议，建议咨询专业兽医获取准确诊断',
      suggestions: suggestions,
      medications: []
    };
    
    this.setData({
      diagnosisResult: result
    });
    
    wx.showModal({
      title: '诊断完成',
      content: '已提供基础诊断建议，建议咨询专业兽医获取准确诊断',
      showCancel: false
    });
  },

  // 保存诊断记录
  saveDiagnosisRecord: function(result) {
    const { health } = require('../../../utils/api');
    
    health.createDiagnosisRecord({
      symptoms: this.data.symptoms,
      diagnosis: result.disease,
      confidence: result.confidence,
      suggestions: result.suggestions.join('; '),
      images: this.data.uploadedImages.map(img => img.path)
    }).then(res => {
      try { const logger = require('../../../utils/logger.js'); logger.info && logger.info('诊断记录保存成功'); } catch(_) {}
    }).catch(err => {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('保存诊断记录失败', err); } catch(_) {}
    });
  },

  // 清空诊断结果
  onClearResult: function () {
    this.setData({
      diagnosisResult: null
    });
  },

  // 保存诊断结果
  onSaveResult: function () {
    wx.showToast({
      title: '已保存到健康记录',
      icon: 'success'
    });
  }
});