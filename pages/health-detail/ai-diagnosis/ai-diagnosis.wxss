/* pages/health/ai-diagnosis/ai-diagnosis.wxss - AI诊断页面样式 V2.0 */
@import '/styles/design-system.wxss';

.diagnosis-container {
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + env(safe-area-inset-bottom));
  min-height: 100vh;
  background: linear-gradient(180deg, var(--bg-secondary) 0%, rgba(240, 255, 244, 0.2) 100%);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.symptom-input {
  width: 100%;
  min-height: 240rpx;
  padding: var(--space-xl);
  box-sizing: border-box;
  background: linear-gradient(135deg, var(--success-subtle) 0%, rgba(240, 255, 244, 0.5) 100%);
  border: 2rpx solid var(--success-light);
  border-radius: var(--radius-2xl);
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-primary);
  transition: var(--transition-all);
  backdrop-filter: blur(5rpx);
}

.symptom-input:focus {
  border-color: var(--success);
  box-shadow: 0 0 0 4rpx var(--success-subtle);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(240, 255, 244, 0.3) 100%);
}

.image-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.uploaded-image {
  position: relative;
  width: 180rpx;
  height: 180rpx;
}

.uploaded-image image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.delete-btn {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #FF3333;
  color: #FFFFFF;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed #CCCCCC;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-btn image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.upload-btn text {
  font-size: 24rpx;
  color: #999999;
}

.upload-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
}

.result-section {
  margin-top: 20rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.confidence {
  font-size: 26rpx;
  color: #0066CC;
  background-color: #E6F4FF;
  padding: 5rpx 15rpx;
  border-radius: 20rpx;
}

.disease-info {
  background: linear-gradient(135deg, var(--warning-subtle) 0%, rgba(255, 249, 230, 0.5) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-2xl);
  border: 2rpx solid var(--warning-light);
  backdrop-filter: blur(5rpx);
  position: relative;
  overflow: hidden;
  animation: slideInCard 0.5s cubic-bezier(0.4, 0, 0.2, 1) backwards;
}

.disease-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--warning) 0%, var(--warning-light) 100%);
}

.disease-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #FF9900;
  margin-bottom: 15rpx;
}

.disease-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.suggestion-item {
  font-size: 26rpx;
  color: #666666;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
  line-height: 1.5;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.medication-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.medication-item:last-child {
  border-bottom: none;
}

.medication-name {
  font-size: 28rpx;
  color: #333333;
}

.medication-dosage {
  font-size: 26rpx;
  color: #0066CC;
}

.result-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 0;
}

.result-actions .btn {
  flex: 1;
}

.btn-default {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--neutral-light) 100%);
  color: var(--text-secondary);
  border: 2rpx solid var(--border-medium);
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  transition: var(--transition-all);
}

.btn-default:active {
  background: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
  color: var(--text-inverse);
  border-color: var(--success);
  transform: translateY(2rpx) scale(0.98);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInCard {
  from {
    opacity: 0;
    transform: translateY(20rpx) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}