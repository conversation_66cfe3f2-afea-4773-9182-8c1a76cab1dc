<!-- pages/task/detail/detail.wxml -->
<view class="container">
  <!-- 任务详情头部 -->
  <view class="task-header">
    <view class="task-status {{task.completed ? 'completed' : 'pending'}}">
      <text class="status-text">{{task.completed ? '已完成' : '待完成'}}</text>
    </view>
    <view class="task-title">
      <text>{{task.title || '任务详情'}}</text>
    </view>
    <view class="task-meta">
      <view class="meta-item">
        <text class="meta-label">创建时间：</text>
        <text class="meta-value">{{task.createTime || '未知'}}</text>
      </view>
      <view class="meta-item">
        <text class="meta-label">截止时间：</text>
        <text class="meta-value">{{task.deadline || task.time || '未设置'}}</text>
      </view>
      <view class="meta-item">
        <text class="meta-label">任务类型：</text>
        <text class="meta-value">{{task.typeText || '未知类型'}}</text>
      </view>
    </view>
  </view>

  <!-- 任务描述 -->
  <view class="card">
    <view class="card-header">
      <text class="card-title">任务描述</text>
    </view>
    <view class="card-content">
      <text class="task-description">{{task.description || '暂无描述'}}</text>
    </view>
  </view>

  <!-- 用药详情 -->
  <view class="card" wx:if="{{task.medicationDetails && task.medicationDetails.length > 0}}">
    <view class="card-header">
      <text class="card-title">用药详情</text>
    </view>
    <view class="card-content">
      <view class="medication-list">
        <view class="medication-item" wx:for="{{task.medicationDetails}}" wx:key="index">
          <view class="medication-header">
            <text class="drug-name">{{item.drug}}</text>
            <text class="drug-dosage">{{item.dosage}}</text>
          </view>
          <view class="medication-details">
            <text class="usage-text">用法：{{item.usage}}</text>
            <text class="duration-text">疗程：{{item.duration}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 任务要求 -->
  <view class="card" wx:if="{{task.requirements && task.requirements.length > 0}}">
    <view class="card-header">
      <text class="card-title">任务要求</text>
    </view>
    <view class="card-content">
      <view class="requirement-list">
        <view class="requirement-item" wx:for="{{task.requirements}}" wx:key="index">
          <text class="requirement-text">• {{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 执行区域 -->
  <view class="card" wx:if="{{task.area}}">
    <view class="card-header">
      <text class="card-title">执行区域</text>
    </view>
    <view class="card-content">
      <text class="area-text">{{task.area}}</text>
    </view>
  </view>

  <!-- 相关链接 -->
  <view class="card" wx:if="{{task.relatedPages && task.relatedPages.length > 0}}">
    <view class="card-header">
      <text class="card-title">相关功能</text>
    </view>
    <view class="card-content">
      <view class="link-list">
        <view class="link-item" wx:for="{{task.relatedPages}}" wx:key="id" bindtap="onRelatedPageTap" data-url="{{item.url}}">
          <text class="link-text">{{item.title}}</text>
          <text class="link-arrow">→</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{!task.completed}}">
    <button class="btn btn-primary btn-lg" bindtap="onCompleteTask">
      <text>标记为完成</text>
    </button>
  </view>

  <!-- 已完成提示 -->
  <view class="completed-notice" wx:if="{{task.completed}}">
    <view class="notice-icon">✓</view>
    <text class="notice-text">此任务已完成</text>
    <text class="notice-time">完成时间：{{task.completedTime || '刚刚'}}</text>
  </view>
</view>