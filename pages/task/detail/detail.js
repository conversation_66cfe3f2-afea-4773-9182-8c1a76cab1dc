// pages/task/detail/detail.js
const { BUSINESS } = require('../../../constants/index.js');

/**
 * 任务详情页面
 * 功能：显示任务详细信息，支持任务完成操作
 */
Page({
  data: {
    taskId: null,
    task: {
      id: null,
      title: '',
      description: '',
      time: '',
      type: '',
      typeText: '',
      completed: false,
      createTime: '',
      deadline: '',
      area: '',
      requirements: [],
      relatedPages: [],
      completedTime: ''
    },
    loading: true
  },

  /**
   * 页面加载
   */
  onLoad: function(options) {
    const taskId = options.id;
    if (!taskId) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 缺少任务ID参数'); } catch(_) {}
      wx.showToast({
        title: '任务ID缺失',
        icon: 'error'
      });
      return;
    }

    this.setData({
      taskId: taskId  // 保持字符串格式，不转换为整数
    });

    this.loadTaskDetail();
  },

  /**
   * 加载任务详情
   */
  loadTaskDetail: function() {
    const { taskId } = this.data;
    
    try {
      // 从统一的任务数据源获取任务
      const { getAllTasks } = require('../../../utils/task-data.js');
      const allTasks = getAllTasks();
      const task = allTasks.find(t => t.id === taskId);
      
      if (!task) {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 未找到对应的任务'); } catch(_) {}
        wx.showToast({
          title: '任务不存在',
          icon: 'error'
        });
        return;
      }

      // 根据任务类型设置类型文本和详细信息
      const typeTextMap = {
        'health': '健康管理',
        'production': '生产管理', 
        'maintenance': '日常维护',
        'monitoring': '环境监控',
        'vaccination': '防疫流程'
      };

      // 为防疫任务添加详细的用药信息
      let medicationDetails = [];
      let requirements = [];
      
      if (task.type === 'vaccination') {
        // 根据任务标题和日龄提供详细的用药信息
        const dayAge = task.dayAge;
        
        switch (dayAge) {
          case 1:
            medicationDetails = [
              { drug: '葡萄糖', dosage: '3%浓度', usage: '饮水添加', duration: '第1天使用' },
              { drug: '红糖', dosage: '3%浓度', usage: '饮水添加（与葡萄糖二选一）', duration: '第1天使用' },
              { drug: '电解多维', dosage: '按说明书比例', usage: '与糖水同时添加', duration: '第1天使用' }
            ];
            requirements = [
              '雏鹅出壳24小时内开始使用',
              '确保饮水器清洁，及时更换药水',
              '观察雏鹅饮水情况和精神状态',
              '控制饮水温度，避免过热或过冷',
              '记录用药时间和雏鹅反应'
            ];
            break;
          case 2:
            medicationDetails = [
              { drug: '小鹅瘟高免血清', dosage: '1.2毫升/只', usage: '颈部皮下注射', duration: '单次使用' },
              { drug: '高免蛋黄抗体', dosage: '1.2毫升/只', usage: '颈部皮下注射（与血清二选一）', duration: '单次使用' },
              { drug: '开口药', dosage: '按说明书', usage: '饮水添加', duration: '第2天继续使用' }
            ];
            requirements = [
              '严格按照1.2毫升/只的剂量注射',
              '注射前检查疫苗是否过期和变质',
              '选择颈部皮下注射，避免注入肌肉',
              '注射后观察15-30分钟，记录不良反应',
              '同时继续开口药的使用'
            ];
            break;
          case 3:
          case 4:
            medicationDetails = [
              { drug: '开口药', dosage: '按说明书', usage: '饮水添加', duration: `第${dayAge}天继续使用` }
            ];
            requirements = [
              '继续开口药的使用，防止病原菌感染',
              '观察弱苗，挑出单独饲喂',
              '确保所有雏鹅都能饮到药水',
              '记录雏鹅精神状态和采食情况',
              '保持环境卫生，定期消毒'
            ];
            break;
          case 5:
            medicationDetails = [
              { drug: '开口药', dosage: '按说明书', usage: '饮水添加', duration: '最后一天使用' }
            ];
            requirements = [
              '开口药最后一天，完成4天疗程',
              '从今天开始控料直到15日龄',
              '加青饲料，不用豆饼等蛋白饲料',
              '晚上12点后停饲料或全喂青草/玉米面',
              '观察弱苗，挑出单独饲喂'
            ];
            break;
          case 6:
            medicationDetails = [
              { drug: '痛风疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '呼肠孤疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '小鹅瘟疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '浆膜炎疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '多维', dosage: '按说明书', usage: '白天饮水添加', duration: '防应激使用' }
            ];
            requirements = [
              '四联疫苗同时注射，总计1毫升/只',
              '疫苗接种后控制棚内湿度在60%以下',
              '接种后24-48小时密切观察鹅群状态',
              '白天饮多维防止疫苗应激',
              '继续控料，加青饲料为主'
            ];
            break;
          case 7:
          case 8:
            medicationDetails = [
              { drug: '痛清', dosage: '2瓶/天', usage: '上午集中2小时饮用', duration: `第${dayAge-6}天使用` },
              { drug: '小苏打', dosage: '千分之三浓度', usage: '晚上10点后饮水添加', duration: `第${dayAge-6}天晚上使用` }
            ];
            requirements = [
              '上午集中2小时内饮完痛清药水',
              '喂药前大群控水半小时左右',
              '晚上10点后添加小苏打，通肝肾效果更佳',
              '继续控料，加青饲料或菜叶',
              '观察鹅群饮水量和精神状态'
            ];
            break;
          case 9:
          case 10:
            medicationDetails = [
              { drug: '呼肠清', dosage: '1瓶/天', usage: '上午集中2小时饮用', duration: `第${dayAge-8}天使用` }
            ];
            requirements = [
              '上午集中2小时内饮完药水',
              '喂药前大群控水半小时左右',
              '确保所有鹅只都能饮到药水',
              '观察是否有呼吸道症状改善',
              '继续控料阶段'
            ];
            break;
          case 11:
          case 14:
            requirements = [
              '继续控料：加青饲料为主',
              '不用豆饼等高蛋白饲料',
              '晚上12点后停饲料或全喂青草',
              '可逐步调整为玉米面喂养',
              '观察鹅群精神状态和生长情况'
            ];
            break;
          case 12:
          case 13:
            medicationDetails = [
              { drug: '亿消2号', dosage: '半袋/天', usage: '上午集中2-3小时饮用', duration: `第${dayAge-11}天使用` },
              { drug: '鸭乐2号', dosage: '半袋/天', usage: '与亿消2号同时使用', duration: `第${dayAge-11}天使用` },
              { drug: '小苏打', dosage: '千分之三浓度', usage: '晚上12点后饮水添加', duration: `第${dayAge-11}天晚上使用` }
            ];
            requirements = [
              '两种药物同时使用，协同预防细菌性疾病',
              '重点预防传染性浆膜炎和大肠杆菌病',
              '上午集中2-3小时内饮完',
              '晚上12点后使用小苏打通肝肾',
              '继续控料阶段'
            ];
            break;
          case 15:
            requirements = [
              '控料最后一天',
              '明天开始恢复正常饲喂',
              '检查控料效果',
              '准备转换到正常饲料',
              '观察鹅群整体状况'
            ];
            break;
          case 16:
          case 17:
            medicationDetails = [
              { drug: '增强素', dosage: '1包/天', usage: '上午集中3小时饮用', duration: `第${dayAge-15}天使用` },
              { drug: '浆速', dosage: '1包/天', usage: '与增强素同时使用', duration: `第${dayAge-15}天使用` }
            ];
            requirements = [
              '两种药物同时使用，预防病毒性感冒',
              '上午集中3小时内饮完',
              '喂药前控水半小时',
              dayAge === 16 ? '从今天开始恢复正常饲喂' : '继续正常饲喂',
              '观察鹅群精神状态'
            ];
            break;
          case 18:
            medicationDetails = [
              { drug: '畅清', dosage: '按情况使用', usage: '观察粪便后决定', duration: '有异常时使用' },
              { drug: '小苏打', dosage: '千分之三浓度', usage: '晚上饮水添加', duration: '当天晚上使用' }
            ];
            requirements = [
              '观察是否有黄白便、水便、黑便',
              '粪便正常则无需用药',
              '有异常可投喂畅清',
              '晚上使用千分之三小苏打通肝肾',
              '密切观察鹅群健康状况'
            ];
            break;
          case 19:
          case 24:
          case 25:
          case 28:
            requirements = [
              '日常观察鹅群健康状况',
              '注意精神状态和采食量',
              '检查粪便是否正常',
              '观察呼吸道症状',
              '记录异常情况及时处理'
            ];
            break;
          case 20:
            medicationDetails = [
              { drug: '副粘病毒疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '禽流感H9疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '安卡拉疫苗', dosage: '1毫升/只', usage: '皮下注射', duration: '单次使用' },
              { drug: '多维', dosage: '按说明书比例', usage: '接种后饮水添加', duration: '接种后3天' }
            ];
            requirements = [
              '三联疫苗同时注射，总计1毫升/只',
              '接种后立即在饮水中添加多维',
              '接种后3天内避免其他应激因素',
              '密切观察接种反应，记录异常情况',
              '保持环境安静，减少惊扰'
            ];
            break;
          case 21:
            medicationDetails = [
              { drug: '亿消2号', dosage: '按情况使用', usage: '观察症状后决定', duration: '有症状时使用' },
              { drug: '小苏打', dosage: '千分之三浓度', usage: '晚上饮水添加', duration: '当天晚上使用' }
            ];
            requirements = [
              '观察是否有甩头流涕咳嗽症状',
              '有症状可喂亿消2号',
              '无症状则不用药',
              '晚上使用千分之三小苏打通肝肾',
              '注意预防感冒'
            ];
            break;
          case 22:
          case 23:
            medicationDetails = [
              { drug: '呼畅', dosage: '1瓶/天', usage: '下午集中3小时饮用', duration: `第${dayAge-21}天使用` }
            ];
            requirements = [
              '下午集中3小时内饮完',
              '喂药前控水半小时',
              '预防小鹅咳嗽呼噜感冒',
              '观察呼吸道症状改善情况',
              '注意环境通风'
            ];
            break;
          case 26:
          case 27:
            medicationDetails = [
              { drug: '肠速清', dosage: '1包/天', usage: '上午集中3小时饮用', duration: `第${dayAge-25}天使用` },
              { drug: '增强素', dosage: '1袋/天', usage: '与肠速清同时使用', duration: `第${dayAge-25}天使用` }
            ];
            requirements = [
              '两种药物同时使用',
              '重点预防肠炎，大杆高峰期',
              '上午集中3小时内饮完',
              '喂药前控水半小时',
              '观察粪便情况和精神状态'
            ];
            break;
          case 29:
          case 30:
            medicationDetails = [
              { drug: '亿消2号', dosage: '1袋/天', usage: '上午集中3小时饮用', duration: `第${dayAge-28}天使用` }
            ];
            requirements = [
              '抗病毒、流感、感冒、副黏病毒、黄病毒',
              '上午集中3小时内饮完',
              '喂药前控水半小时',
              dayAge === 30 ? '完成第一个月关键防疫阶段' : '继续抗病毒治疗',
              dayAge === 30 ? '后续可平稳养殖，30天后可备用其他抗生素' : '观察治疗效果'
            ];
            break;
          default:
            medicationDetails = [
              { drug: '具体用药', dosage: '按标准剂量', usage: '按规定方式', duration: '按疗程使用' }
            ];
            requirements = [
              '严格按照防疫流程执行',
              '注意用药剂量和时间',
              '观察接种后反应',
              '记录执行情况',
              '如有异常及时处理'
            ];
        }
      } else {
        requirements = task.requirements || [
          '按照标准流程执行',
          '记录详细操作信息',
          '如有问题及时上报'
        ];
      }

      // 设置任务详情数据
      const taskDetail = {
        ...task,
        typeText: typeTextMap[task.type] || task.type,
        medicationDetails: medicationDetails, // 添加用药详情
        requirements: requirements, // 使用动态生成的要求
        area: task.area || (task.type === 'vaccination' ? '全场区域' : '指定区域')
      };

      // 从本地存储读取任务完成状态
      const completedTasks = wx.getStorageSync('completed_tasks') || {};
      const taskStatus = completedTasks[taskId];
      
      if (taskStatus && taskStatus.completed) {
        taskDetail.completed = true;
        taskDetail.completedTime = taskStatus.completedTime;
      }

      this.setData({
        task: taskDetail,
        loading: false
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: taskDetail.title
      });
    } catch (error) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 加载任务详情失败', error); } catch(_) {}
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  /**
   * 完成任务
   */
  onCompleteTask: function() {
    wx.showModal({
      title: '确认完成',
      content: '确定要标记此任务为已完成吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 标记任务为完成
            const now = new Date();
            const completedTime = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
            
            this.setData({
              'task.completed': true,
              'task.completedTime': completedTime
            });

            // 保存任务完成状态到本地存储
            let completedTasks = wx.getStorageSync('completed_tasks') || {};
            completedTasks[this.data.taskId] = {
              completed: true,
              completedTime: completedTime
            };
            wx.setStorageSync('completed_tasks', completedTasks);

            wx.showToast({
              title: '任务已完成',
              icon: 'success'
            });

            // 2秒后返回上一页
            setTimeout(() => {
              wx.navigateBack();
            }, 2000);
          } catch (error) {
            try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 任务完成操作失败', error); } catch(_) {}
            wx.showToast({
              title: '操作失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 跳转到相关页面
   */
  onRelatedPageTap: function(e) {
    const url = e.currentTarget.dataset.url;
    
    if (!url) {
      try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 相关页面URL为空'); } catch(_) {}
      return;
    }

    wx.navigateTo({
      url: url,
      fail: (err) => {
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('[TaskDetail] 跳转失败', err); } catch(_) {}
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function() {
    return {
      title: `任务：${this.data.task.title}`,
      path: `/pages/task/detail/detail?id=${this.data.taskId}`
    };
  }
});