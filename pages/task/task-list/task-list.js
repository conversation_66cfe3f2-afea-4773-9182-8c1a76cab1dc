// pages/task/task-list/task-list.js
Page({
  data: {
    activeFilter: 'all',
    searchKeyword: '',
    sortBy: 'deadline',
    showSortModal: false,
    loading: false,
    tasks: [],
    filteredTasks: [],
    taskCounts: {
      all: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      overdue: 0
    }
  },

  onLoad: function (options) {
    this.loadTasks();
  },

  onPullDownRefresh: function () {
    this.loadTasks(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    // 加载更多任务
    this.loadMoreTasks();
  },

  // 加载任务列表
  loadTasks(callback) {
    this.setData({ loading: true });

    // 使用统一的任务数据源
    setTimeout(() => {
      try {
        const { getAllTasks } = require('../../../utils/task-data.js');
        const tasks = getAllTasks();
        
        this.setData({
          tasks: tasks,
          loading: false
        });
        
        this.filterTasks();
        this.calculateTaskCounts();
        callback && callback();
      } catch (error) {
        console.error('加载任务数据失败:', error);
        this.setData({
          loading: false
        });
        callback && callback();
      }
    }, 500);
  },

  // 筛选任务
  filterTasks() {
    let filtered = [...this.data.tasks];
    
    // 仅显示未完成的任务
    filtered = filtered.filter(task => !task.completed);
    
    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(task => 
        task.title.toLowerCase().includes(keyword) ||
        task.description.toLowerCase().includes(keyword) ||
        task.category.toLowerCase().includes(keyword)
      );
    }
    
    // 排序
    this.sortTasks(filtered);
    
    this.setData({
      filteredTasks: filtered
    });
  },

  // 排序任务
  sortTasks(tasks) {
    const { sortBy } = this.data;
    
    tasks.sort((a, b) => {
      switch (sortBy) {
      case 'deadline':
        return new Date(a.deadline) - new Date(b.deadline);
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      case 'createTime':
        return new Date(b.createTime) - new Date(a.createTime);
      case 'status':
        const statusOrder = { pending: 1, processing: 2, overdue: 3, completed: 4 };
        return statusOrder[a.status] - statusOrder[b.status];
      default:
        return 0;
      }
    });
  },

  // 计算任务数量
  calculateTaskCounts() {
    const { tasks } = this.data;
    // 只计算未完成的任务
    const pendingTasks = tasks.filter(t => !t.completed);
    const counts = {
      all: pendingTasks.length,
      pending: pendingTasks.filter(t => t.status === 'pending').length,
      processing: pendingTasks.filter(t => t.status === 'processing').length,
      completed: 0, // 不显示已完成的任务
      overdue: pendingTasks.filter(t => t.status === 'overdue').length
    };
    
    this.setData({ taskCounts: counts });
  },

  // 切换筛选条件
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter
    });
    this.filterTasks();
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterTasks();
  },

  // 执行搜索
  onSearch() {
    this.filterTasks();
  },

  // 清除搜索
  onClearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.filterTasks();
  },

  // 显示排序弹窗
  onSortTap() {
    this.setData({
      showSortModal: true
    });
  },

  // 关闭排序弹窗
  onCloseSortModal() {
    this.setData({
      showSortModal: false
    });
  },

  // 选择排序方式
  onSortSelect(e) {
    const sortBy = e.currentTarget.dataset.sort;
    this.setData({
      sortBy: sortBy,
      showSortModal: false
    });
    this.filterTasks();
  },

  // 点击任务项
  onTaskTap(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task/detail/detail?id=${taskId}`
    });
  },

  // 开始任务
  onStartTask(e) {
    const taskId = e.currentTarget.dataset.id;
    this.updateTaskStatus(taskId, 'processing', '任务已开始');
  },

  // 完成任务
  onCompleteTask(e) {
    e.stopPropagation(); // 阻止事件冒泡
    const taskId = e.currentTarget.dataset.id;
    
    // 使用统一的任务管理
    const { completeTask } = require('../../../utils/task-data.js');
    completeTask(taskId, new Date().toLocaleString());
    
    // 显示提示
    wx.showToast({
      title: '任务已完成',
      icon: 'success',
      duration: 1500
    });

    // 刷新任务列表
    setTimeout(() => {
      this.loadTasks();
    }, 1000);
  },

  // 查看任务
  onViewTask(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task/detail/detail?id=${taskId}`
    });
  },

  // 重启任务
  onRestartTask(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '确认重启',
      content: '确定要重启这个任务吗？',
      success: (res) => {
        if (res.confirm) {
          this.updateTaskStatus(taskId, 'pending', '任务已重启');
        }
      }
    });
  },

  // 更新任务状态
  updateTaskStatus(taskId, newStatus, message) {
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        const statusMap = {
          pending: '待处理',
          processing: '进行中',
          completed: '已完成',
          overdue: '已逾期'
        };
        return {
          ...task,
          status: newStatus,
          statusText: statusMap[newStatus]
        };
      }
      return task;
    });

    this.setData({ tasks });
    this.filterTasks();
    this.calculateTaskCounts();

    wx.showToast({
      title: message,
      icon: 'success'
    });
  },



  // 加载更多任务
  loadMoreTasks() {
    // 模拟加载更多
    wx.showToast({
      title: '没有更多任务了',
      icon: 'none'
    });
  },

  /**
   * 阻止事件冒泡的通用方法
   */
  stopPropagation(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
  }
});
