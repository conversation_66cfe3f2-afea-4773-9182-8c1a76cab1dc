/* 任务完成按钮样式 */
.complete-btn {
  background: #f5f5f5 !important;
  color: #333333 !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 60rpx;
  transition: all 0.3s ease;
}

.complete-btn:active {
  background: #e6f7ff !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
}

/* 任务列表容器 */
.task-list-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 筛选栏 */
.filter-section {
  background-color: #ffffff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  width: 100%;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
  white-space: nowrap;
}

.filter-tab {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #f8f9fa;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.filter-tab.active {
  background-color: #0066cc;
  color: #ffffff;
}

/* 搜索栏 */
.search-section {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  margin-left: 12rpx;
}

.clear-btn image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
}

.sort-btn image {
  width: 24rpx;
  height: 24rpx;
}

/* 任务列表 */
.task-list {
  padding: 20rpx;
}

/* 任务项基础样式 */
.task-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid #e0e0e0;
  border-top: 2rpx solid transparent;
  border-right: 2rpx solid transparent;
  border-bottom: 2rpx solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

/* 根据日龄阶段设置不同的框线颜色 */
/* 开口药阶段（1-5天）- 紫色系 */
.task-item[data-day-age="1"],
.task-item[data-day-age="2"],
.task-item[data-day-age="3"],
.task-item[data-day-age="4"],
.task-item[data-day-age="5"] {
  border-left-color: #722ed1;
  border-top-color: rgba(114, 46, 209, 0.3);
  border-right-color: rgba(114, 46, 209, 0.3);
  border-bottom-color: rgba(114, 46, 209, 0.3);
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.05) 0%, #ffffff 100%);
}

/* 疫苗接种阶段（6天）- 深红色系 */
.task-item[data-day-age="6"] {
  border-left-color: #c41e3a;
  border-top-color: rgba(196, 30, 58, 0.3);
  border-right-color: rgba(196, 30, 58, 0.3);
  border-bottom-color: rgba(196, 30, 58, 0.3);
  background: linear-gradient(135deg, rgba(196, 30, 58, 0.05) 0%, #ffffff 100%);
}

/* 保肝护肾阶段（7-8天）- 橙色系 */
.task-item[data-day-age="7"],
.task-item[data-day-age="8"] {
  border-left-color: #fa8c16;
  border-top-color: rgba(250, 140, 22, 0.3);
  border-right-color: rgba(250, 140, 22, 0.3);
  border-bottom-color: rgba(250, 140, 22, 0.3);
  background: linear-gradient(135deg, rgba(250, 140, 22, 0.05) 0%, #ffffff 100%);
}

/* 呼肠孤防治阶段（9-10天）- 青色系 */
.task-item[data-day-age="9"],
.task-item[data-day-age="10"] {
  border-left-color: #13c2c2;
  border-top-color: rgba(19, 194, 194, 0.3);
  border-right-color: rgba(19, 194, 194, 0.3);
  border-bottom-color: rgba(19, 194, 194, 0.3);
  background: linear-gradient(135deg, rgba(19, 194, 194, 0.05) 0%, #ffffff 100%);
}

/* 控料阶段（11,14,15天）- 棕色系 */
.task-item[data-day-age="11"],
.task-item[data-day-age="14"],
.task-item[data-day-age="15"] {
  border-left-color: #8b4513;
  border-top-color: rgba(139, 69, 19, 0.3);
  border-right-color: rgba(139, 69, 19, 0.3);
  border-bottom-color: rgba(139, 69, 19, 0.3);
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, #ffffff 100%);
}

/* 浆膜炎防治阶段（12-13天）- 深蓝色系 */
.task-item[data-day-age="12"],
.task-item[data-day-age="13"] {
  border-left-color: #1890ff;
  border-top-color: rgba(24, 144, 255, 0.3);
  border-right-color: rgba(24, 144, 255, 0.3);
  border-bottom-color: rgba(24, 144, 255, 0.3);
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, #ffffff 100%);
}

/* 病毒性感冒防治阶段（16-17天）- 绿色系 */
.task-item[data-day-age="16"],
.task-item[data-day-age="17"] {
  border-left-color: #52c41a;
  border-top-color: rgba(82, 196, 26, 0.3);
  border-right-color: rgba(82, 196, 26, 0.3);
  border-bottom-color: rgba(82, 196, 26, 0.3);
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, #ffffff 100%);
}

/* 痛风预防阶段（18天）- 玫红色系 */
.task-item[data-day-age="18"] {
  border-left-color: #eb2f96;
  border-top-color: rgba(235, 47, 150, 0.3);
  border-right-color: rgba(235, 47, 150, 0.3);
  border-bottom-color: rgba(235, 47, 150, 0.3);
  background: linear-gradient(135deg, rgba(235, 47, 150, 0.05) 0%, #ffffff 100%);
}

/* 日常观察阶段（19,24,25,28天）- 灰色系 */
.task-item[data-day-age="19"],
.task-item[data-day-age="24"],
.task-item[data-day-age="25"],
.task-item[data-day-age="28"] {
  border-left-color: #8c8c8c;
  border-top-color: rgba(140, 140, 140, 0.3);
  border-right-color: rgba(140, 140, 140, 0.3);
  border-bottom-color: rgba(140, 140, 140, 0.3);
  background: linear-gradient(135deg, rgba(140, 140, 140, 0.05) 0%, #ffffff 100%);
}

/* 第三针疫苗阶段（20天）- 深紫色系 */
.task-item[data-day-age="20"] {
  border-left-color: #531dab;
  border-top-color: rgba(83, 29, 171, 0.3);
  border-right-color: rgba(83, 29, 171, 0.3);
  border-bottom-color: rgba(83, 29, 171, 0.3);
  background: linear-gradient(135deg, rgba(83, 29, 171, 0.05) 0%, #ffffff 100%);
}

/* 感冒预防阶段（21天）- 粉色系 */
.task-item[data-day-age="21"] {
  border-left-color: #f759ab;
  border-top-color: rgba(247, 89, 171, 0.3);
  border-right-color: rgba(247, 89, 171, 0.3);
  border-bottom-color: rgba(247, 89, 171, 0.3);
  background: linear-gradient(135deg, rgba(247, 89, 171, 0.05) 0%, #ffffff 100%);
}

/* 呼吸道疾病预防阶段（22-23天）- 天蓝色系 */
.task-item[data-day-age="22"],
.task-item[data-day-age="23"] {
  border-left-color: #40a9ff;
  border-top-color: rgba(64, 169, 255, 0.3);
  border-right-color: rgba(64, 169, 255, 0.3);
  border-bottom-color: rgba(64, 169, 255, 0.3);
  background: linear-gradient(135deg, rgba(64, 169, 255, 0.05) 0%, #ffffff 100%);
}

/* 肠炎+抗病毒阶段（26-27天）- 黄绿色系 */
.task-item[data-day-age="26"],
.task-item[data-day-age="27"] {
  border-left-color: #a0d911;
  border-top-color: rgba(160, 217, 17, 0.3);
  border-right-color: rgba(160, 217, 17, 0.3);
  border-bottom-color: rgba(160, 217, 17, 0.3);
  background: linear-gradient(135deg, rgba(160, 217, 17, 0.05) 0%, #ffffff 100%);
}

/* 最终抗病毒阶段（29-30天）- 金色系 */
.task-item[data-day-age="29"],
.task-item[data-day-age="30"] {
  border-left-color: #fadb14;
  border-top-color: rgba(250, 219, 20, 0.3);
  border-right-color: rgba(250, 219, 20, 0.3);
  border-bottom-color: rgba(250, 219, 20, 0.3);
  background: linear-gradient(135deg, rgba(250, 219, 20, 0.05) 0%, #ffffff 100%);
}

/* 已完成任务的特殊样式 */
.task-item.completed {
  border-left-color: #52c41a;
  border-top-color: rgba(82, 196, 26, 0.5);
  border-right-color: rgba(82, 196, 26, 0.5);
  border-bottom-color: rgba(82, 196, 26, 0.5);
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1) 0%, #ffffff 100%);
  opacity: 0.8;
}

/* 过期任务的特殊样式 */
.task-item.overdue {
  border-left-color: #ff4d4f;
  border-top-color: rgba(255, 77, 79, 0.5);
  border-right-color: rgba(255, 77, 79, 0.5);
  border-bottom-color: rgba(255, 77, 79, 0.5);
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.1) 0%, #ffffff 100%);
}

/* 保持原有的优先级颜色作为备用 */
.task-item.high {
  border-left-color: #ff4d4f;
}

.task-item.medium {
  border-left-color: #faad14;
}

.task-item.low {
  border-left-color: #52c41a;
}

.task-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.task-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.task-status.pending {
  background-color: #fff7e6;
  color: #faad14;
}

.task-status.processing {
  background-color: #e6f7ff;
  color: #1890ff;
}

.task-status.completed {
  background-color: #e8f5e8;
  color: #52c41a;
}

.task-status.overdue {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.task-priority {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.task-priority.high {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.task-priority.medium {
  background-color: #fff7e6;
  color: #faad14;
}

.task-priority.low {
  background-color: #e8f5e8;
  color: #52c41a;
}

.task-content {
  margin-bottom: 16rpx;
}

.task-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.task-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.meta-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 22rpx;
  color: #999999;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.action-btn.primary {
  background-color: #0066cc;
  color: #ffffff;
}

.action-btn.success {
  background-color: #52c41a;
  color: #ffffff;
}

.action-btn.secondary {
  background-color: #f8f9fa;
  color: #666666;
}

.action-btn.warning {
  background-color: #faad14;
  color: #ffffff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

.add-task-btn {
  background-color: #0066cc;
  color: #ffffff;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border: none;
}

.add-task-btn::after {
  border: none;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #666666;
}



/* 排序弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.sort-modal {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  width: 100%;
  max-height: 60vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.close-btn image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

.sort-options {
  display: flex;
  flex-direction: column;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333333;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #0066cc;
}

.sort-option image {
  width: 32rpx;
  height: 32rpx;
}