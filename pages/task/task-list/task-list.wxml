<!-- pages/task/task-list/task-list.wxml -->
<view class="task-list-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <image class="search-icon" src="/images/icons/search.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索任务..." value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
      <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
        <image src="/images/icons/close.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="sort-btn" bindtap="onSortTap">
      <image src="/images/icons/sort.png" mode="aspectFit"></image>
      <text>排序</text>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-list">
    <block wx:for="{{filteredTasks}}" wx:key="id">
      <view class="task-item {{item.priority}} {{item.status === 'completed' ? 'completed' : ''}} {{item.status === 'overdue' ? 'overdue' : ''}}" 
            bindtap="onTaskTap" 
            data-id="{{item.id}}" 
            data-day-age="{{item.dayAge}}">
        <view class="task-header">
          <view class="task-status {{item.status}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
          <view class="task-priority {{item.priority}}">
            <text class="priority-text">{{item.priorityText}}</text>
          </view>
        </view>
        
        <view class="task-content">
          <text class="task-title">{{item.title}}</text>
          <text class="task-desc">{{item.description}}</text>
          
          <view class="task-meta">
            <view class="meta-item">
              <image class="meta-icon" src="/images/icons/time.png" mode="aspectFit"></image>
              <text class="meta-text">{{item.deadline}}</text>
            </view>
            <view class="meta-item" wx:if="{{item.assignee}}">
              <image class="meta-icon" src="/images/icons/user.png" mode="aspectFit"></image>
              <text class="meta-text">{{item.assignee}}</text>
            </view>
            <view class="meta-item" wx:if="{{item.category}}">
              <image class="meta-icon" src="/images/icons/tag.png" mode="aspectFit"></image>
              <text class="meta-text">{{item.category}}</text>
            </view>
          </view>
        </view>

        <view class="task-actions">
          <view class="action-btn complete-btn" bindtap="onCompleteTask" data-id="{{item.id}}" catchtap="stopPropagation">
            完成
          </view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view wx:if="{{filteredTasks.length === 0 && !loading}}" class="empty-state">
      <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
      <text class="empty-text">{{searchKeyword ? '没有找到相关任务' : '暂无任务'}}</text>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text>加载中...</text>
    </view>
  </view>



  <!-- 排序弹窗 -->
  <view wx:if="{{showSortModal}}" class="modal-overlay" bindtap="onCloseSortModal">
    <view class="sort-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">排序方式</text>
        <view class="close-btn" bindtap="onCloseSortModal">
          <image src="/images/icons/close.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="sort-options">
        <view class="sort-option {{sortBy === 'deadline' ? 'active' : ''}}" data-sort="deadline" bindtap="onSortSelect">
          <text>按截止时间</text>
          <image wx:if="{{sortBy === 'deadline'}}" src="/images/icons/check.png" mode="aspectFit"></image>
        </view>
        <view class="sort-option {{sortBy === 'priority' ? 'active' : ''}}" data-sort="priority" bindtap="onSortSelect">
          <text>按优先级</text>
          <image wx:if="{{sortBy === 'priority'}}" src="/images/icons/check.png" mode="aspectFit"></image>
        </view>
        <view class="sort-option {{sortBy === 'createTime' ? 'active' : ''}}" data-sort="createTime" bindtap="onSortSelect">
          <text>按创建时间</text>
          <image wx:if="{{sortBy === 'createTime'}}" src="/images/icons/check.png" mode="aspectFit"></image>
        </view>
        <view class="sort-option {{sortBy === 'status' ? 'active' : ''}}" data-sort="status" bindtap="onSortSelect">
          <text>按状态</text>
          <image wx:if="{{sortBy === 'status'}}" src="/images/icons/check.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
</view>
