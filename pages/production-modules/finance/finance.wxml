<!-- pages/production/finance/finance.wxml -->
<view class="finance-container">
  <!-- 财务统计内容 -->
  <view class="finance-content">
    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <block wx:for="{{timeRanges}}" wx:key="id">
        <text class="range-item {{activeTimeRange === item.id ? 'active' : ''}}" data-range="{{item.id}}" bindtap="onTimeRangeChange">{{item.name}}</text>
      </block>
    </view>

    <!-- 财务概览 -->
    <view class="section overview-section">
      <view class="section-header">
        <text class="section-title">财务概览</text>
        <view class="finance-nav-actions">
          <view class="nav-item" bindtap="onFinanceNavTap" data-type="overview">
            <text class="nav-icon">📊</text>
            <text class="nav-text">财务概览</text>
          </view>
          <view class="nav-item" bindtap="onFinanceNavTap" data-type="reports">
            <text class="nav-icon">📈</text>
            <text class="nav-text">财务报表</text>
          </view>
        </view>
      </view>
      <view class="overview-grid">
        <view class="overview-item income" bindtap="onOverviewItemTap" data-type="income">
          <text class="item-label">总收入</text>
          <text class="item-value">¥{{financeOverview.income}}</text>
        </view>
        <view class="overview-item expense" bindtap="onOverviewItemTap" data-type="expense">
          <text class="item-label">总支出</text>
          <text class="item-value">¥{{financeOverview.expense}}</text>
        </view>
        <view class="overview-item profit" bindtap="onOverviewItemTap" data-type="profit">
          <text class="item-label">净利润</text>
          <text class="item-value">¥{{financeOverview.profit}}</text>
        </view>
        <view class="overview-item margin" bindtap="onOverviewItemTap" data-type="margin">
          <text class="item-label">利润率</text>
          <text class="item-value">{{financeOverview.profitMargin}}%</text>
        </view>
      </view>
    </view>

    <!-- AI分析结果 -->
    <view class="section ai-analysis-section" wx:if="{{aiAnalysisResult}}">
      <view class="section-header">
        <text class="section-title">AI财务分析报告</text>
        <text class="close-analysis" bindtap="onCloseAIAnalysis">×</text>
      </view>
      <view class="ai-analysis-content">
        <view class="analysis-item" wx:if="{{aiAnalysisResult.overview}}">
          <text class="analysis-label">数据概况</text>
          <text class="analysis-text">{{aiAnalysisResult.overview}}</text>
        </view>
        <view class="analysis-item" wx:if="{{aiAnalysisResult.keyIndicators}}">
          <text class="analysis-label">关键指标</text>
          <text class="analysis-text">{{aiAnalysisResult.keyIndicators}}</text>
        </view>
        <view class="analysis-item" wx:if="{{aiAnalysisResult.trends}}">
          <text class="analysis-label">趋势分析</text>
          <text class="analysis-text">{{aiAnalysisResult.trends}}</text>
        </view>
        <view class="analysis-item" wx:if="{{aiAnalysisResult.suggestions}}">
          <text class="analysis-label">改进建议</text>
          <text class="analysis-text">{{aiAnalysisResult.suggestions}}</text>
        </view>
        <view class="analysis-item" wx:if="{{aiAnalysisResult.risks}}">
          <text class="analysis-label">风险提示</text>
          <text class="analysis-text">{{aiAnalysisResult.risks}}</text>
        </view>
        <view class="ai-analysis-footer">
          <text class="ai-disclaimer">*AI分析仅供参考，具体决策请结合实际情况</text>
        </view>
      </view>
    </view>

    <!-- 财务趋势图表 -->
    <view class="section" wx:if="{{selectedChartType}}">
      <c-trend-chart
        title="{{chartTitle}}"
        subtitle="财务数据趋势分析"
        chart-data="{{financeChartData}}"
        chart-config="{{financeChartConfig}}"
        time-ranges="{{timeRanges}}"
        active-time-range="{{activeTimeRange}}"
        show-time-filter="{{true}}"
        legend-data="{{financeLegendData}}"
        show-current-values="{{false}}"
        show-footer="{{true}}"
        update-time="{{updateTime}}"
        allow-export="{{false}}"
        allow-fullscreen="{{false}}"
        canvas-width="{{350}}"
        canvas-height="{{280}}"
        bind:timeRangeChange="onFinanceTimeRangeChange"
        bind:rendered="onFinanceChartRendered"
        bind:refresh="onRefreshFinanceChart"
      />
      <view class="close-chart-btn" bindtap="onCloseChart">
        <text>关闭图表</text>
      </view>
    </view>



    <!-- 财务记录 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">财务记录</text>
      </view>

      <!-- 记录类型筛选 -->
      <view class="record-filter">
        <view class="filter-item {{recordFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="onRecordFilterChange">
          <text>全部</text>
        </view>
        <view class="filter-item {{recordFilter === 'income' ? 'active' : ''}}" data-filter="income" bindtap="onRecordFilterChange">
          <text>收入</text>
        </view>
        <view class="filter-item {{recordFilter === 'expense' ? 'active' : ''}}" data-filter="expense" bindtap="onRecordFilterChange">
          <text>支出</text>
        </view>
      </view>
      <view class="records-list">
        <block wx:for="{{financeRecords}}" wx:key="id">
          <view class="record-item" bindtap="onRecordItemTap" data-record="{{item}}">
            <view class="record-info">
              <view class="record-type {{item.type === '收入' ? 'income' : 'expense'}}">{{item.type}}</view>
              <view class="record-content">
                <text class="record-category">{{item.category}}</text>
                <text class="record-remark">{{item.remark}}</text>
              </view>
            </view>
            <view class="record-right">
              <view class="record-amount {{item.type === '收入' ? 'income' : 'expense'}}">
                {{item.type === '收入' ? '+' : '-'}}¥{{item.amount}}
              </view>
              <view class="record-date">{{item.date}}</view>
              <text class="record-arrow">></text>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <view wx:if="{{financeRecords.length === 0 && !loading}}" class="empty-state">
          <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
          <text class="empty-text">暂无财务记录</text>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{hasMore && financeRecords.length > 0}}" class="load-more">
          <text>加载中...</text>
        </view>

        <!-- 没有更多 -->
        <view wx:elif="{{!hasMore && financeRecords.length > 0}}" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </view>
    </view>

    <!-- 底部添加记录按钮 -->
    <view class="bottom-actions">
      <button class="add-record-btn" bindtap="onAddRecord">+ 添加记录</button>
    </view>
  </view>

  <!-- 记录详情弹窗 -->
  <view class="modal-overlay" wx:if="{{showDetailModal}}" bindtap="onCloseDetailModal">
    <view class="modal-content detail-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">财务记录详情</text>
        <text class="modal-close" bindtap="onCloseDetailModal">×</text>
      </view>

      <view class="modal-body">
        <view class="detail-section">
          <view class="detail-item">
            <text class="detail-label">记录类型</text>
            <text class="detail-value {{selectedRecord.type === '收入' ? 'income' : 'expense'}}">{{selectedRecord.type}}</text>
          </view>

          <view class="detail-item">
            <text class="detail-label">分类</text>
            <text class="detail-value">{{selectedRecord.category}}</text>
          </view>

          <view class="detail-item">
            <text class="detail-label">金额</text>
            <text class="detail-value amount {{selectedRecord.type === '收入' ? 'income' : 'expense'}}">
              {{selectedRecord.type === '收入' ? '+' : '-'}}¥{{selectedRecord.amount}}
            </text>
          </view>

          <view class="detail-item">
            <text class="detail-label">日期</text>
            <text class="detail-value">{{selectedRecord.date}}</text>
          </view>

          <view class="detail-item">
            <text class="detail-label">备注</text>
            <text class="detail-value">{{selectedRecord.remark}}</text>
          </view>

          <!-- 关联业务记录 -->
          <view class="detail-item" wx:if="{{selectedRecord.relatedRecord}}">
            <text class="detail-label">关联记录</text>
            <view class="related-record" bindtap="onViewRelatedRecord">
              <text class="related-type">{{selectedRecord.relatedRecord.type}}</text>
              <text class="related-desc">{{selectedRecord.relatedRecord.description}}</text>
              <text class="related-arrow">></text>
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-secondary" bindtap="onCloseDetailModal">关闭</button>
        <button class="btn-primary" bindtap="onEditRecord" wx:if="{{selectedRecord.canEdit}}">编辑</button>
      </view>
    </view>
  </view>

  <!-- 添加记录弹窗 -->
  <view class="modal-overlay" wx:if="{{showAddModal}}" bindtap="onCloseAddModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">添加财务记录</text>
        <text class="modal-close" bindtap="onCloseAddModal">×</text>
      </view>

      <view class="modal-body">
        <!-- 记录类型 -->
        <view class="form-group">
          <text class="form-label">记录类型</text>
          <view class="radio-group">
            <view class="radio-item {{newRecord.type === '收入' ? 'active' : ''}}" bindtap="onRecordTypeChange" data-type="收入">
              <text>收入</text>
            </view>
            <view class="radio-item {{newRecord.type === '支出' ? 'active' : ''}}" bindtap="onRecordTypeChange" data-type="支出">
              <text>支出</text>
            </view>
          </view>
        </view>

        <!-- 分类选择 -->
        <view class="form-group">
          <text class="form-label">分类</text>
          <picker mode="selector" range="{{categoryOptions}}" value="{{newRecord.categoryIndex}}" bindchange="onCategoryChange">
            <view class="picker-input">
              <text class="{{newRecord.category ? '' : 'placeholder'}}">{{newRecord.category || '请选择分类'}}</text>
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>

        <!-- 金额输入 -->
        <view class="form-group">
          <text class="form-label">金额</text>
          <input class="form-input" type="digit" placeholder="请输入金额" value="{{newRecord.amount}}" bindinput="onAmountInput" />
        </view>

        <!-- 备注输入 -->
        <view class="form-group">
          <text class="form-label">备注</text>
          <textarea class="form-textarea" placeholder="请输入备注信息" value="{{newRecord.remark}}" bindinput="onRemarkInput" maxlength="100"></textarea>
        </view>

        <!-- 日期选择 -->
        <view class="form-group">
          <text class="form-label">日期</text>
          <picker mode="date" value="{{newRecord.date}}" bindchange="onDateChange">
            <view class="picker-input">
              <text>{{newRecord.date}}</text>
              <text class="picker-arrow">></text>
            </view>
          </picker>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="onCloseAddModal">取消</button>
        <button class="btn-confirm" bindtap="onConfirmAdd" disabled="{{!canSubmit}}">确认添加</button>
      </view>
    </view>
  </view>
</view>