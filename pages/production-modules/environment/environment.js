// pages/production/environment/environment.js
const authHelper = require('../../../utils/auth-helper.js');

Page({
  data: {
    // 当前环境数据
    currentData: {
      temperature: 0,
      humidity: 0,
      pm25: 0,
      light: 0,
      updateTime: ''
    },
    
    // 历史数据
    historyData: {
      temperature: [],
      humidity: [],
      pm25: [],
      light: []
    },
    
    // 时间范围选项
    timeRanges: [
      { label: '近24小时', value: '24h' },
      { label: '近7天', value: '7d' },
      { label: '近30天', value: '30d' }
    ],
    activeTimeRange: '24h',
    
    // 报警信息
    alerts: [],
    
    // 正常范围
    normalRanges: {
      temperature: { min: 15, max: 30 },
      humidity: { min: 50, max: 80 },
      pm25: { min: 0, max: 50 },
      light: { min: 200, max: 2000 }
    },
    
    loading: true,

    // 选中的趋势类型
    selectedTrendType: '',

    // 趋势数据
    trendData: [],

    // 图表数据
    chartData: [],
    
    // 图表配置
    chartConfig: {
      type: 'line',
      smooth: true,
      showPoints: true,
      showGrid: true,
      animation: true
    },

    // 图例数据
    legendData: [],

    // 当前图表值
    currentChartValues: null
  },

  onLoad: function (options) {
  // 检查页面权限
    const hasPermission = authHelper.checkPagePermission(options, 'production:environment:read', () => {
      // 权限检查通过，加载数据
      this.loadData();
    });

    if (!hasPermission) {
      return; // 权限检查失败，不继续执行
    }
  },

  onShow: function () {
    // 页面显示
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadData(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载数据
  loadData: function (callback) {
    this.setData({
      loading: true
    });

    // 模拟加载数据
    setTimeout(() => {
      // 模拟当前环境数据
      const currentData = {
        temperature: 26.5,
        humidity: 65,
        pm25: 32,
        light: 800,
        updateTime: '2023-06-15 14:30'
      };

      // 模拟历史数据（近24小时，每2小时一个点）
      const historyData = {
        temperature: [
          { time: '14:30', value: 26.5 },
          { time: '12:30', value: 27.2 },
          { time: '10:30', value: 26.8 },
          { time: '08:30', value: 25.9 },
          { time: '06:30', value: 24.5 },
          { time: '04:30', value: 23.8 },
          { time: '02:30', value: 23.2 },
          { time: '00:30', value: 22.9 },
          { time: '22:30', value: 23.5 },
          { time: '20:30', value: 24.2 },
          { time: '18:30', value: 25.1 },
          { time: '16:30', value: 25.8 }
        ],
        humidity: [
          { time: '14:30', value: 65 },
          { time: '12:30', value: 67 },
          { time: '10:30', value: 68 },
          { time: '08:30', value: 70 },
          { time: '06:30', value: 72 },
          { time: '04:30', value: 74 },
          { time: '02:30', value: 75 },
          { time: '00:30', value: 73 },
          { time: '22:30', value: 71 },
          { time: '20:30', value: 69 },
          { time: '18:30', value: 67 },
          { time: '16:30', value: 66 }
        ],
        pm25: [
          { time: '14:30', value: 32 },
          { time: '12:30', value: 35 },
          { time: '10:30', value: 38 },
          { time: '08:30', value: 42 },
          { time: '06:30', value: 45 },
          { time: '04:30', value: 48 },
          { time: '02:30', value: 46 },
          { time: '00:30', value: 44 },
          { time: '22:30', value: 40 },
          { time: '20:30', value: 38 },
          { time: '18:30', value: 35 },
          { time: '16:30', value: 33 }
        ],
        light: [
          { time: '14:30', value: 800 },
          { time: '12:30', value: 1200 },
          { time: '10:30', value: 800 },
          { time: '08:30', value: 400 },
          { time: '06:30', value: 200 },
          { time: '04:30', value: 50 },
          { time: '02:30', value: 20 },
          { time: '00:30', value: 10 },
          { time: '22:30', value: 150 },
          { time: '20:30', value: 300 },
          { time: '18:30', value: 550 },
          { time: '16:30', value: 750 }
        ]
      };

      // 模拟报警信息
      const alerts = [
        { id: 1, type: 'warning', message: '温度过高', time: '2023-06-15 12:30', status: '未处理' },
        { id: 2, type: 'info', message: '湿度过低', time: '2023-06-15 06:30', status: '已处理' }
      ];

      this.setData({
        currentData: currentData,
        historyData: historyData,
        alerts: alerts,
        loading: false
      });

      callback && callback();
    }, 500);
  },

  // 切换时间范围
  onTimeRangeChange: function (e) {
    let range;
    
    // 支持两种调用方式：直接点击和组件事件
    if (e.detail && e.detail.value) {
      range = e.detail.value;
    } else {
      range = e.currentTarget.dataset.range;
    }

    this.setData({
      activeTimeRange: range
    });
    
    // 重新生成对应时间范围的图表数据
    if (this.data.selectedTrendType) {
      this.generateChartData(this.data.selectedTrendType, range);
    }
  },

  // 查看报警详情
  onViewAlert: function (e) {
    const alert = e.currentTarget.dataset.alert;
    
    if (!alert) {
      wx.showToast({
        title: '数据异常',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showAlertDetailModal: true,
      selectedAlert: {
        ...alert,
        detailInfo: this.generateAlertDetail(alert)
      }
    });
  },

  // 生成报警详细信息
  generateAlertDetail: function(alert) {
    const detailInfo = {
      basicInfo: {
        id: alert.id || Date.now(),
        type: alert.type,
        level: alert.level,
        message: alert.message,
        time: alert.time || new Date().toISOString(),
        status: alert.status || 'active'
      },
      environmentData: {
        currentValue: alert.currentValue || 0,
        thresholdValue: alert.thresholdValue || 0,
        deviation: Math.abs((alert.currentValue || 0) - (alert.thresholdValue || 0)),
        unit: this.getUnitByType(alert.type)
      },
      historicalData: this.generateHistoricalData(alert.type),
      suggestions: this.generateSuggestions(alert.type, alert.level),
      actions: this.generateActionPlan(alert.type, alert.level)
    };

    return detailInfo;
  },

  // 根据类型获取单位
  getUnitByType: function(type) {
    const units = {
      temperature: '°C',
      humidity: '%',
      ammonia: 'ppm',
      co2: 'ppm',
      light: 'lux',
      noise: 'dB'
    };
    return units[type] || '';
  },

  // 生成历史数据
  generateHistoricalData: function(type) {
    const data = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const hour = time.getHours();
      
      // 模拟不同类型的数据波动
      let value;
      switch (type) {
      case 'temperature':
        value = 20 + Math.sin(hour * Math.PI / 12) * 5 + (Math.random() - 0.5) * 2;
        break;
      case 'humidity':
        value = 60 + Math.sin(hour * Math.PI / 12) * 10 + (Math.random() - 0.5) * 5;
        break;
      case 'ammonia':
        value = 10 + Math.random() * 15;
        break;
      default:
        value = Math.random() * 100;
      }
      
      data.push({
        time: time.toISOString(),
        value: parseFloat(value.toFixed(2)),
        hour: hour
      });
    }
    
    return data;
  },

  // 生成建议
  generateSuggestions: function(type, level) {
    const suggestions = {
      temperature: {
        high: ['开启通风设备', '增加遮阳措施', '检查保温系统'],
        medium: ['调节室内温度', '监控变化趋势'],
        low: ['正常监控即可']
      },
      humidity: {
        high: ['增加除湿设备', '改善通风条件', '检查漏水点'],
        medium: ['适当通风', '持续观察'],
        low: ['正常监控即可']
      },
      ammonia: {
        high: ['立即清理粪便', '增强通风', '检查排污系统'],
        medium: ['加强清洁', '改善空气流通'],
        low: ['定期清洁即可']
      }
    };

    return suggestions[type]?.[level] || ['请联系技术人员'];
  },

  // 生成处理方案
  generateActionPlan: function(type, level) {
    const actions = [
      {
        step: 1,
        title: '立即评估',
        content: '确认当前环境状况，评估对鹅群的潜在影响',
        urgent: level === 'high'
      },
      {
        step: 2,
        title: '采取措施',
        content: '根据预警类型执行相应的调节措施',
        urgent: level === 'high'
      },
      {
        step: 3,
        title: '持续监控',
        content: '密切关注参数变化，确保环境逐步恢复正常',
        urgent: false
      },
      {
        step: 4,
        title: '记录分析',
        content: '记录处理过程和效果，为今后预防提供参考',
        urgent: false
      }
    ];

    return actions;
  },

  // 关闭报警详情模态框
  onCloseAlertDetailModal: function() {
    this.setData({
      showAlertDetailModal: false,
      selectedAlert: null
    });
  },

  // 确认已处理报警
  onConfirmAlertHandled: function() {
    const alert = this.data.selectedAlert;
    
    // 更新报警状态
    const alerts = this.data.alerts.map(item => {
      if (item.id === alert.basicInfo.id) {
        return {
          ...item,
          status: 'handled',
          handledTime: new Date().toISOString()
        };
      }
      return item;
    });

    this.setData({
      alerts: alerts,
      showAlertDetailModal: false,
      selectedAlert: null
    });

    wx.showToast({
      title: '已标记为已处理',
      icon: 'success'
    });
  },

  // 设置报警阈值
  onSetThreshold: function (e) {
    const type = e?.currentTarget?.dataset?.type || 'all';
    
    this.setData({
      showThresholdModal: true,
      thresholdSettings: this.initThresholdSettings(type)
    });
  },

  // 初始化阈值设置
  initThresholdSettings: function(type) {
    const defaultThresholds = {
      temperature: {
        name: '温度',
        unit: '°C',
        min: 18,
        max: 28,
        warningMin: 16,
        warningMax: 30,
        enabled: true
      },
      humidity: {
        name: '湿度',
        unit: '%',
        min: 50,
        max: 80,
        warningMin: 40,
        warningMax: 90,
        enabled: true
      },
      ammonia: {
        name: '氨气浓度',
        unit: 'ppm',
        min: 0,
        max: 20,
        warningMin: 0,
        warningMax: 30,
        enabled: true
      },
      co2: {
        name: '二氧化碳',
        unit: 'ppm',
        min: 300,
        max: 1000,
        warningMin: 200,
        warningMax: 1500,
        enabled: true
      },
      light: {
        name: '光照强度',
        unit: 'lux',
        min: 100,
        max: 800,
        warningMin: 50,
        warningMax: 1000,
        enabled: true
      },
      noise: {
        name: '噪音',
        unit: 'dB',
        min: 30,
        max: 70,
        warningMin: 25,
        warningMax: 80,
        enabled: true
      }
    };

    // 如果指定了特定类型，只返回该类型的设置
    if (type !== 'all' && defaultThresholds[type]) {
      return {
        [type]: defaultThresholds[type]
      };
    }

    return defaultThresholds;
  },

  // 阈值输入变化
  onThresholdInput: function(e) {
    const { type, field } = e.currentTarget.dataset;
    const value = parseFloat(e.detail.value) || 0;
    
    this.setData({
      [`thresholdSettings.${type}.${field}`]: value
    });
  },

  // 启用/禁用阈值监控
  onToggleThreshold: function(e) {
    const { type } = e.currentTarget.dataset;
    const enabled = e.detail.value;
    
    this.setData({
      [`thresholdSettings.${type}.enabled`]: enabled
    });
  },

  // 重置为默认值
  onResetThreshold: function(e) {
    const { type } = e.currentTarget.dataset;
    const defaultSettings = this.initThresholdSettings('all');
    
    if (defaultSettings[type]) {
      this.setData({
        [`thresholdSettings.${type}`]: defaultSettings[type]
      });

      wx.showToast({
        title: '已重置为默认值',
        icon: 'success'
      });
    }
  },

  // 保存阈值设置
  onSaveThresholds: function() {
    const settings = this.data.thresholdSettings;
    
    // 验证阈值设置
    const validationResult = this.validateThresholds(settings);
    if (!validationResult.isValid) {
      wx.showToast({
        title: validationResult.message,
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...'
    });

    // 模拟保存到服务器
    setTimeout(() => {
      // 保存到本地存储
      wx.setStorageSync('environmentThresholds', settings);
      
      // 更新当前页面的阈值
      this.setData({
        thresholds: settings,
        showThresholdModal: false
      });

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 重新检查当前环境数据是否超过新阈值
      this.checkThresholdAlerts();
    }, 1500);
  },

  // 验证阈值设置
  validateThresholds: function(settings) {
    for (let type in settings) {
      const threshold = settings[type];
      
      if (!threshold.enabled) continue;

      // 检查最小值不能大于最大值
      if (threshold.min >= threshold.max) {
        return {
          isValid: false,
          message: `${threshold.name}的最小值不能大于等于最大值`
        };
      }

      // 检查预警值的合理性
      if (threshold.warningMin >= threshold.min) {
        return {
          isValid: false,
          message: `${threshold.name}的预警最小值应小于正常最小值`
        };
      }

      if (threshold.warningMax <= threshold.max) {
        return {
          isValid: false,
          message: `${threshold.name}的预警最大值应大于正常最大值`
        };
      }
    }

    return { isValid: true };
  },

  // 检查阈值报警
  checkThresholdAlerts: function() {
    const currentData = this.data.envData;
    const thresholds = this.data.thresholds;
    const alerts = [];

    Object.keys(currentData).forEach(type => {
      if (!thresholds[type] || !thresholds[type].enabled) return;

      const value = currentData[type];
      const threshold = thresholds[type];
      
      let alertLevel = null;
      let message = '';

      if (value < threshold.warningMin || value > threshold.warningMax) {
        alertLevel = 'high';
        message = `${threshold.name}严重超标: ${value}${threshold.unit}`;
      } else if (value < threshold.min || value > threshold.max) {
        alertLevel = 'medium';
        message = `${threshold.name}超出正常范围: ${value}${threshold.unit}`;
      }

      if (alertLevel) {
        alerts.push({
          id: `${type}_${Date.now()}`,
          type: type,
          level: alertLevel,
          message: message,
          currentValue: value,
          thresholdValue: alertLevel === 'high' ? 
            (value < threshold.warningMin ? threshold.warningMin : threshold.warningMax) :
            (value < threshold.min ? threshold.min : threshold.max),
          time: new Date().toISOString(),
          status: 'active'
        });
      }
    });

    if (alerts.length > 0) {
      this.setData({
        alerts: [...alerts, ...this.data.alerts]
      });
    }
  },

  // 关闭阈值设置模态框
  onCloseThresholdModal: function() {
    this.setData({
      showThresholdModal: false,
      thresholdSettings: {}
    });
  },

  // 查看趋势图
  onViewTrend: function (e) {
    const type = e.currentTarget.dataset.type;
    const typeNames = {
      temperature: '温度',
      humidity: '湿度',
      pm25: 'PM2.5',
      light: '光照'
    };

    const typeName = typeNames[type];
    if (!typeName) return;

    this.setData({
      selectedTrendType: typeName
    });

    // 生成图表数据
    this.generateChartData(type, this.data.activeTimeRange);
  },

  // 生成图表数据
  generateChartData: function(type, timeRange) {
    const typeConfigs = {
      temperature: { unit: '°C', color: '#FF6B6B', min: 15, max: 35 },
      humidity: { unit: '%', color: '#4ECDC4', min: 40, max: 90 },
      pm25: { unit: '', color: '#45B7D1', min: 0, max: 100 },
      light: { unit: 'lx', color: '#FFA726', min: 0, max: 2000 }
    };

    const config = typeConfigs[type];
    if (!config) return;

    // 根据时间范围生成不同数量的数据点
    let dataCount, timeFormat, timeStep;
    switch (timeRange) {
    case '24h':
      dataCount = 12; // 每2小时一个点
      timeFormat = 'hour';
      timeStep = 2;
      break;
    case '7d':
      dataCount = 7; // 每天一个点
      timeFormat = 'day';
      timeStep = 1;
      break;
    case '30d':
      dataCount = 15; // 每2天一个点
      timeFormat = 'day';
      timeStep = 2;
      break;
    default:
      dataCount = 12;
      timeFormat = 'hour';
      timeStep = 2;
    }

    // 生成模拟数据
    const chartData = [];
    const now = new Date();
    
    for (let i = dataCount - 1; i >= 0; i--) {
      const time = new Date(now);
      
      if (timeFormat === 'hour') {
        time.setHours(time.getHours() - i * timeStep);
      } else {
        time.setDate(time.getDate() - i * timeStep);
      }

      // 生成符合正常范围的随机值，并添加一些变化趋势
      const baseValue = (config.min + config.max) / 2;
      const variation = (config.max - config.min) * 0.3;
      const trendFactor = Math.sin((i / dataCount) * Math.PI * 2) * 0.2;
      const randomFactor = (Math.random() - 0.5) * 0.6;
      
      let value = baseValue + variation * (trendFactor + randomFactor);
      value = Math.max(config.min, Math.min(config.max, value));

      chartData.push({
        time: timeFormat === 'hour' 
          ? `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
          : `${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')}`,
        value: Math.round(value * 10) / 10
      });
    }

    // 设置图例数据
    const legendData = [{
      key: type,
      label: this.data.selectedTrendType,
      color: config.color
    }];

    // 设置当前值显示
    const currentChartValues = {
      time: chartData[chartData.length - 1]?.time || '',
      values: [{
        key: type,
        label: this.data.selectedTrendType,
        value: chartData[chartData.length - 1]?.value || 0,
        unit: config.unit,
        color: config.color
      }]
    };

    this.setData({
      chartData: chartData,
      legendData: legendData,
      currentChartValues: currentChartValues
    });
  },

  // 图表渲染完成
  onChartRendered: function(e) {
  },

  // 刷新图表
  onRefreshChart: function() {
    if (this.data.selectedTrendType) {
      // 从selectedTrendType反推type
      const typeNames = {
        '温度': 'temperature',
        '湿度': 'humidity',
        'PM2.5': 'pm25',
        '光照': 'light'
      };
      
      const type = typeNames[this.data.selectedTrendType];
      if (type) {
        this.generateChartData(type, this.data.activeTimeRange);
      }
    }
  },

  // 关闭图表
  onCloseChart: function() {
    this.setData({
      selectedTrendType: '',
      chartData: [],
      legendData: [],
      currentChartValues: null
    });
  }
});