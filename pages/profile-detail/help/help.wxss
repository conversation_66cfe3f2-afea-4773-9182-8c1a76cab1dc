/* 帮助中心页面样式 */
.help-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
  box-sizing: border-box;
  padding-bottom: 120rpx;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 20rpx;
  position: relative;
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  font-size: 32rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  margin-left: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.search-btn:active {
  transform: scale(0.95);
}

/* 搜索建议 */
.search-suggestions {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.suggestion-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.3s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: #f8f9fa;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
}

/* 搜索结果 */
.search-results {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.results-count {
  font-size: 24rpx;
  color: #666;
}

.result-section {
  margin-bottom: 24rpx;
}

.result-section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.category-results,
.question-results {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.category-result-item,
.question-result-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.category-result-item:active,
.question-result-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.category-result-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.category-result-title,
.question-result-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.question-result-arrow {
  font-size: 24rpx;
  color: #999;
}

.no-results {
  text-align: center;
  padding: 60rpx 0;
}

.no-results-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
}

.no-results-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.no-results-tips {
  font-size: 24rpx;
  color: #999;
  display: block;
}



/* 区块 */
.section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #0066cc;
  border-radius: 4rpx;
}

/* 帮助分类 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.category-item:active {
  background: #e9ecef;
  transform: scale(0.95);
  border-color: #007AFF;
}

.category-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.category-count {
  font-size: 22rpx;
  color: #999;
}

.view-more {
  font-size: 26rpx;
  color: #007AFF;
  margin-left: auto;
}

/* 常见问题 */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.faq-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  transition: background 0.3s ease;
}

.faq-question:active {
  background: #e9ecef;
}

.question-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 20rpx 20rpx;
  border-top: 1rpx solid #e9ecef;
  background: #ffffff;
}

.answer-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  display: block;
}

.answer-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.helpful-btn,
.unhelpful-btn {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.helpful-btn:active {
  background: #d4edda;
  color: #155724;
}

.unhelpful-btn:active {
  background: #f8d7da;
  color: #721c24;
}

/* 热门教程 */
.tutorial-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tutorial-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tutorial-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.tutorial-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.tutorial-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.tutorial-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.tutorial-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.tutorial-meta {
  display: flex;
  gap: 16rpx;
  margin-top: 4rpx;
}

.tutorial-views,
.tutorial-time {
  font-size: 22rpx;
  color: #999;
}

.tutorial-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 联系我们 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.contact-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.contact-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.contact-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.contact-value {
  font-size: 26rpx;
  color: #666;
}

.contact-action {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}





/* 响应式适配 */
@media (max-width: 480rpx) {
  .category-grid {
    grid-template-columns: 1fr;
  }

  .help-container {
    padding: 16rpx;
  }
}

/* 动画效果 */
.section {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}