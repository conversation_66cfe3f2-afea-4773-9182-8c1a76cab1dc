<!-- pages/profile/settings/settings.wxml -->
<view class="settings-container">
  <!-- 用户信息设置 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">用户信息</text>
    </view>
    <view class="settings-list">
      <view class="setting-item">
        <text class="setting-label">姓名</text>
        <input class="setting-input" value="{{userSettings.name}}" data-field="name" bindinput="onUserInfoInput" />
      </view>
      <view class="setting-item">
        <text class="setting-label">手机号</text>
        <input class="setting-input" value="{{userSettings.phone}}" data-field="phone" bindinput="onUserInfoInput" />
      </view>
      <view class="setting-item">
        <text class="setting-label">邮箱</text>
        <input class="setting-input" value="{{userSettings.email}}" data-field="email" bindinput="onUserInfoInput" />
      </view>
      <view class="setting-item">
        <text class="setting-label">养殖场名称</text>
        <input class="setting-input" value="{{userSettings.farmName}}" data-field="farmName" bindinput="onUserInfoInput" />
      </view>
    </view>
  </view>
  
  <!-- 系统设置 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">系统设置</text>
    </view>
    <view class="settings-list">
      <view class="setting-item switch-item">
        <text class="setting-label">消息通知</text>
        <switch checked="{{systemSettings.notification}}" data-field="notification" bindchange="onSystemSettingChange" />
      </view>
      <view class="setting-item switch-item">
        <text class="setting-label">声音提醒</text>
        <switch checked="{{systemSettings.sound}}" data-field="sound" bindchange="onSystemSettingChange" />
      </view>
      <view class="setting-item switch-item">
        <text class="setting-label">自动同步</text>
        <switch checked="{{systemSettings.autoSync}}" data-field="autoSync" bindchange="onSystemSettingChange" />
      </view>
      <view class="setting-item">
        <text class="setting-label">主题</text>
        <picker mode="selector" range="{{['浅色', '深色']}}" value="{{systemSettings.theme === 'light' ? 0 : 1}}" data-field="theme" bindchange="onThemeChange">
          <view class="picker-value">
            <text>{{systemSettings.theme === 'light' ? '浅色' : '深色'}}</text>
            <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
          </view>
        </picker>
      </view>
    </view>
  </view>
  
  <!-- 通知设置 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">通知设置</text>
    </view>
    <view class="settings-list">
      <view class="setting-item switch-item">
        <text class="setting-label">健康预警通知</text>
        <switch checked="{{notificationSettings.healthAlert}}" data-field="healthAlert" bindchange="onNotificationSettingChange" />
      </view>
      <view class="setting-item switch-item">
        <text class="setting-label">环境异常通知</text>
        <switch checked="{{notificationSettings.environmentAlert}}" data-field="environmentAlert" bindchange="onNotificationSettingChange" />
      </view>
      <view class="setting-item switch-item">
        <text class="setting-label">物料不足通知</text>
        <switch checked="{{notificationSettings.materialAlert}}" data-field="materialAlert" bindchange="onNotificationSettingChange" />
      </view>
      <view class="setting-item switch-item">
        <text class="setting-label">财务预警通知</text>
        <switch checked="{{notificationSettings.financeAlert}}" data-field="financeAlert" bindchange="onNotificationSettingChange" />
      </view>
    </view>
  </view>
  
  <!-- 其他设置 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">其他</text>
    </view>
    <view class="settings-list">
      <view class="setting-item" bindtap="onChangePassword">
        <text class="setting-label">修改密码</text>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
      <view class="setting-item" bindtap="onClearCache">
        <text class="setting-label">清除缓存</text>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
      <view class="setting-item" bindtap="onHelp">
        <text class="setting-label">帮助中心</text>
        <image class="arrow-icon" src="/images/icon_arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
  
  <!-- 保存按钮 -->
  <view class="save-section">
    <button class="save-btn" bindtap="onSaveSettings">保存设置</button>
  </view>
</view>

<!-- 修改密码模态框 -->
<modal wx:if="{{showPasswordModal}}" class="password-modal">
  <view class="modal-overlay" bindtap="onClosePasswordModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">修改密码</text>
      <button class="modal-close" bindtap="onClosePasswordModal">×</button>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label">当前密码</text>
        <input 
          class="form-input" 
          type="password" 
          placeholder="请输入当前密码" 
          value="{{passwordData.oldPassword}}"
          data-field="oldPassword"
          bindinput="onPasswordInput"
        />
      </view>
      <view class="form-group">
        <text class="form-label">新密码</text>
        <input 
          class="form-input" 
          type="password" 
          placeholder="请输入新密码（至少6位）" 
          value="{{passwordData.newPassword}}"
          data-field="newPassword"
          bindinput="onPasswordInput"
        />
      </view>
      <view class="form-group">
        <text class="form-label">确认新密码</text>
        <input 
          class="form-input" 
          type="password" 
          placeholder="请再次输入新密码" 
          value="{{passwordData.confirmPassword}}"
          data-field="confirmPassword"
          bindinput="onPasswordInput"
        />
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel-btn" bindtap="onClosePasswordModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="onConfirmChangePassword">确认修改</button>
    </view>
  </view>
</modal>