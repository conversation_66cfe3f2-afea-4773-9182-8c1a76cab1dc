/* pages/profile/settings/settings.wxss */
.settings-container {
  min-height: 100vh;
  background-color: #f5f6f8;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 区块 */
.section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #0066cc;
  border-radius: 4rpx;
}

/* 设置列表 */
.settings-list {
  padding: 10rpx 0;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.setting-input {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #666666;
}

.switch-item {
  padding: 15rpx 0;
}

.picker-value {
  display: flex;
  align-items: center;
}

.picker-value text {
  font-size: 28rpx;
  color: #666666;
  margin-right: 10rpx;
}

.arrow-icon {
  width: 30rpx;
  height: 30rpx;
  opacity: 0.7;
}

/* 保存按钮 */
.save-section {
  padding: 40rpx 0 20rpx;
}

.save-btn {
  width: 100%;
  height: 80rpx;
  background-color: #0066cc;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
}

.save-btn:hover {
  background-color: var(--primary-dark);
}

/* 修改密码模态框样式 */
.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666666;
}

.modal-close:hover {
  background-color: #e0e0e0;
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #0066cc;
  background-color: #ffffff;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  height: 70rpx;
  padding: 0 30rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666666;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.confirm-btn {
  background-color: #0066cc;
  color: #ffffff;
}

.confirm-btn:hover {
  background-color: var(--primary-dark);
}