{"pages": ["pages/home/<USER>", "pages/login/login", "pages/production/production", "pages/shop/shop", "pages/profile/profile", "pages/more/more"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#0066CC", "navigationBarTitleText": "智慧养鹅", "navigationBarTextStyle": "white", "backgroundColor": "#f5f5f5"}, "tabBar": {"custom": false, "color": "#7A7E83", "selectedColor": "#0066CC", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/home/<USER>", "text": "首页", "iconPath": "assets/icons/home.png", "selectedIconPath": "assets/icons/home_selected.png"}, {"pagePath": "pages/production/production", "text": "生产", "iconPath": "assets/icons/production.png", "selectedIconPath": "assets/icons/production_selected.png"}, {"pagePath": "pages/shop/shop", "text": "商城", "iconPath": "assets/icons/shop.png", "selectedIconPath": "assets/icons/shop_selected.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "assets/icons/profile.png", "selectedIconPath": "assets/icons/profile_selected.png"}]}, "networkTimeout": {"request": 15000, "downloadFile": 15000, "uploadFile": 15000, "connectSocket": 15000}, "debug": false, "lazyCodeLoading": "requiredComponents", "subPackages": [{"root": "pages/production-detail", "name": "production-detail", "pages": ["record-list/record-list", "record-detail/record-detail", "material-detail/material-detail", "knowledge/knowledge", "knowledge/detail/detail", "report/report", "ai-diagnosis/ai-diagnosis"]}, {"root": "pages/shop-detail", "name": "shop-detail", "pages": ["goods-detail/goods-detail", "goods/add/add", "cart/cart", "checkout/checkout", "order-success/order-success"]}, {"root": "pages/production-modules", "name": "production-modules", "pages": ["environment/environment", "finance/finance", "record-add/record-add", "reimbursement/add/add", "reimbursement/detail/detail"]}, {"root": "pages/profile-detail", "name": "profile-detail", "pages": ["settings/settings", "help/help"]}, {"root": "pages/orders", "name": "orders", "pages": ["orders"]}, {"root": "pages/order-detail", "name": "order-detail", "pages": ["order-detail"]}, {"root": "pages/payment", "name": "payment", "pages": ["payment"]}, {"root": "pages/logistics", "name": "logistics", "pages": ["logistics"]}, {"root": "pages/address", "name": "address", "pages": ["address", "edit/edit"]}, {"root": "pages/announcement", "name": "announcement", "pages": ["announcement-list/announcement-list", "announcement-detail/announcement-detail"]}, {"root": "pages/price", "name": "price", "pages": ["price-detail/price-detail"]}, {"root": "pages/inventory", "name": "inventory", "pages": ["inventory-detail/inventory-detail"]}, {"root": "pages/task", "name": "task", "pages": ["task-list/task-list", "detail/detail"]}, {"root": "pages/weather", "name": "weather", "pages": ["weather-detail/weather-detail"]}, {"root": "pages/workspace", "name": "workspace", "pages": ["workspace", "expense/list/list", "expense/detail/detail", "expense/apply/apply", "expense/approve/approve", "payment/list/list", "payment/detail/detail", "payment/apply/apply", "payment/approve/approve", "contract/list/list", "contract/detail/detail", "contract/apply/apply", "contract/approve/approve", "activity/list/list", "activity/detail/detail", "activity/apply/apply", "activity/approve/approve", "reserve/list/list", "reserve/detail/detail", "reserve/apply/apply", "reserve/approve/approve", "purchase/list/list", "purchase/detail/detail", "purchase/apply/apply", "purchase/approve/approve", "finance/overview/overview", "finance/reports/reports", "reports/reports", "approval/pending/pending", "approval/history/history"]}, {"root": "pages/ai-config", "name": "ai-config", "pages": ["ai-config"]}, {"root": "pages/ai-stats", "name": "ai-stats", "pages": ["ai-stats"]}, {"root": "pages/vaccination", "name": "vaccination", "pages": ["task-list/task-list", "task-detail/task-detail"]}, {"root": "constants-data", "name": "constants-data", "pages": []}], "preloadRule": {"pages/home/<USER>": {"network": "wifi", "packages": ["production-detail", "production-modules", "vaccination"]}, "pages/shop/shop": {"network": "all", "packages": ["shop-detail"]}, "pages/workspace/workspace": {"network": "wifi", "packages": ["workspace"]}, "pages/production/production": {"network": "wifi", "packages": ["production-detail"]}, "pages/shop-detail/goods-detail/goods-detail": {"network": "all", "packages": ["orders", "payment"]}, "pages/production-modules/environment/environment": {"network": "wifi", "packages": ["task", "weather"]}, "pages/profile/profile": {"network": "wifi", "packages": ["profile-detail"]}}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredBackgroundModes": ["audio"], "plugins": {}, "resizable": true, "sitemapLocation": "sitemap.json"}